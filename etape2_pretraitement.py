#!/usr/bin/env python3
"""
ÉTAPE 2 - Prétraitement des données textuelles
Prétraite les données collectées dans l'Étape 1 pour améliorer la qualité des analyses NLP
"""

import re
import string
import logging
from pymongo import MongoClient
from datetime import datetime
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer
from bs4 import BeautifulSoup

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PreprocesseurTexte:
    def __init__(self):
        """Initialise le préprocesseur de texte"""
        # Configuration MongoDB
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]
        
        # Initialiser NLTK
        self._initialiser_nltk()
        
        # Initialiser les outils de prétraitement
        self.lemmatizer = WordNetLemmatizer()
        self.stop_words = set(stopwords.words('english'))
        
        logger.info("Préprocesseur de texte initialisé")
        logger.info(f"Mots vides chargés: {len(self.stop_words)} mots")
    
    def _initialiser_nltk(self):
        """Télécharge les ressources NLTK nécessaires"""
        try:
            nltk.download('punkt_tab', quiet=True)
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
            nltk.download('wordnet', quiet=True)
            nltk.download('omw-1.4', quiet=True)
            logger.info("Ressources NLTK téléchargées")
        except Exception as e:
            logger.warning(f"Erreur téléchargement NLTK: {e}")
    
    def nettoyer_html(self, texte):
        """Supprime les balises HTML"""
        if not texte:
            return ""
        
        # Utiliser BeautifulSoup pour supprimer les balises HTML
        soup = BeautifulSoup(texte, 'html.parser')
        texte_propre = soup.get_text()
        
        # Supprimer les espaces multiples
        texte_propre = re.sub(r'\s+', ' ', texte_propre)
        
        return texte_propre.strip()
    
    def supprimer_urls(self, texte):
        """Supprime les URLs du texte"""
        if not texte:
            return ""
        
        # Pattern pour détecter les URLs
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        texte = re.sub(url_pattern, '', texte)
        
        # Pattern pour détecter www.
        www_pattern = r'www\.(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        texte = re.sub(www_pattern, '', texte)
        
        return texte.strip()
    
    def supprimer_caracteres_speciaux(self, texte):
        """Supprime les caractères spéciaux"""
        if not texte:
            return ""
        
        # Garder seulement les lettres, espaces et quelques caractères de base
        texte = re.sub(r'[^\w\s]', ' ', texte)
        
        # Supprimer les espaces multiples
        texte = re.sub(r'\s+', ' ', texte)
        
        return texte.strip()
    
    def supprimer_ponctuation_chiffres(self, texte):
        """Supprime la ponctuation et les chiffres"""
        if not texte:
            return ""
        
        # Supprimer la ponctuation
        texte = texte.translate(str.maketrans('', '', string.punctuation))
        
        # Supprimer les chiffres
        texte = re.sub(r'\d+', '', texte)
        
        # Supprimer les espaces multiples
        texte = re.sub(r'\s+', ' ', texte)
        
        return texte.strip()
    
    def supprimer_mots_vides(self, texte):
        """Supprime les mots vides (stopwords)"""
        if not texte:
            return ""
        
        # Tokeniser le texte
        tokens = word_tokenize(texte.lower())
        
        # Supprimer les mots vides
        tokens_filtres = [token for token in tokens if token not in self.stop_words and len(token) > 2]
        
        return ' '.join(tokens_filtres)
    
    def lemmatiser_texte(self, texte):
        """Applique la lemmatisation"""
        if not texte:
            return ""
        
        # Tokeniser le texte
        tokens = word_tokenize(texte.lower())
        
        # Lemmatiser chaque token
        tokens_lemmatises = [self.lemmatizer.lemmatize(token) for token in tokens if token.isalpha()]
        
        return ' '.join(tokens_lemmatises)
    
    def pretraiter_texte_complet(self, texte):
        """Applique tout le prétraitement sur un texte"""
        if not texte:
            return ""
        
        # Étape 1: Convertir en minuscules
        texte = texte.lower()
        
        # Étape 2: Supprimer les balises HTML
        texte = self.nettoyer_html(texte)
        
        # Étape 3: Supprimer les URLs
        texte = self.supprimer_urls(texte)
        
        # Étape 4: Supprimer les caractères spéciaux
        texte = self.supprimer_caracteres_speciaux(texte)
        
        # Étape 5: Supprimer la ponctuation et les chiffres
        texte = self.supprimer_ponctuation_chiffres(texte)
        
        # Étape 6: Supprimer les mots vides
        texte = self.supprimer_mots_vides(texte)
        
        # Étape 7: Lemmatisation
        texte = self.lemmatiser_texte(texte)
        
        return texte.strip()
    
    def pretraiter_document(self, document):
        """Prétraite un document complet"""
        try:
            # Prétraiter le titre
            titre_original = document.get('titre', '')
            titre_pretraite = self.pretraiter_texte_complet(titre_original)
            
            # Prétraiter le contenu
            contenu_original = document.get('contenu', '')
            contenu_pretraite = self.pretraiter_texte_complet(contenu_original)
            
            # Créer le document prétraité
            document_pretraite = document.copy()
            document_pretraite.update({
                'titre_pretraite': titre_pretraite,
                'contenu_pretraite': contenu_pretraite,
                'texte_complet_pretraite': f"{titre_pretraite} {contenu_pretraite}".strip(),
                'date_pretraitement': datetime.now(),
                'pretraitement_effectue': True
            })
            
            return document_pretraite
            
        except Exception as e:
            logger.error(f"Erreur prétraitement document {document.get('_id', 'unknown')}: {e}")
            return None
    
    def pretraiter_tous_documents(self):
        """Prétraite tous les documents de la collection"""
        logger.info("=" * 60)
        logger.info("DÉBUT PRÉTRAITEMENT - ÉTAPE 2")
        logger.info("=" * 60)
        
        # Compter les documents à traiter
        total_documents = self.collection.count_documents({})
        documents_non_traites = self.collection.count_documents({"pretraitement_effectue": {"$ne": True}})
        
        logger.info(f"Total documents en base: {total_documents}")
        logger.info(f"Documents à prétraiter: {documents_non_traites}")
        
        if documents_non_traites == 0:
            logger.info("Tous les documents sont déjà prétraités")
            return 0
        
        documents_traites = 0
        documents_erreur = 0
        
        # Traiter les documents non prétraités
        cursor = self.collection.find({"pretraitement_effectue": {"$ne": True}})
        
        for document in cursor:
            document_pretraite = self.pretraiter_document(document)
            
            if document_pretraite:
                # Mettre à jour le document dans la base
                self.collection.replace_one(
                    {"_id": document["_id"]}, 
                    document_pretraite
                )
                documents_traites += 1
                
                if documents_traites % 10 == 0:
                    logger.info(f"Prétraités: {documents_traites}/{documents_non_traites}")
            else:
                documents_erreur += 1
        
        # Résumé
        logger.info("=" * 60)
        logger.info("RÉSUMÉ PRÉTRAITEMENT")
        logger.info("=" * 60)
        logger.info(f"Documents traités avec succès: {documents_traites}")
        logger.info(f"Documents en erreur: {documents_erreur}")
        logger.info(f"Total documents prétraités en base: {self.collection.count_documents({'pretraitement_effectue': True})}")
        
        return documents_traites
    
    def afficher_exemple_pretraitement(self, limite=3):
        """Affiche des exemples de prétraitement"""
        logger.info("\n" + "📝 EXEMPLES DE PRÉTRAITEMENT")
        logger.info("-" * 40)
        
        # Récupérer quelques documents prétraités
        documents = list(self.collection.find({"pretraitement_effectue": True}).limit(limite))
        
        for i, doc in enumerate(documents, 1):
            logger.info(f"\nExemple {i}:")
            logger.info(f"Titre original: {doc.get('titre', '')[:80]}...")
            logger.info(f"Titre prétraité: {doc.get('titre_pretraite', '')[:80]}...")
            logger.info(f"Contenu original: {doc.get('contenu', '')[:100]}...")
            logger.info(f"Contenu prétraité: {doc.get('contenu_pretraite', '')[:100]}...")
    
    def obtenir_statistiques_pretraitement(self):
        """Obtient les statistiques du prétraitement"""
        total = self.collection.count_documents({})
        pretraites = self.collection.count_documents({"pretraitement_effectue": True})
        
        logger.info(f"\n📊 STATISTIQUES PRÉTRAITEMENT")
        logger.info(f"Total documents: {total}")
        logger.info(f"Documents prétraités: {pretraites}")
        logger.info(f"Pourcentage prétraité: {(pretraites/total*100):.1f}%" if total > 0 else "0%")
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("Connexions fermées")

def main():
    """Fonction principale"""
    preprocesseur = None
    
    try:
        # Initialiser le préprocesseur
        preprocesseur = PreprocesseurTexte()
        
        # Lancer le prétraitement
        documents_traites = preprocesseur.pretraiter_tous_documents()
        
        # Afficher des exemples
        preprocesseur.afficher_exemple_pretraitement()
        
        # Afficher les statistiques
        preprocesseur.obtenir_statistiques_pretraitement()
        
        if documents_traites > 0:
            logger.info("\n✅ ÉTAPE 2 TERMINÉE AVEC SUCCÈS !")
            logger.info("✅ Conversion en minuscules")
            logger.info("✅ Suppression balises HTML, URLs, caractères spéciaux")
            logger.info("✅ Suppression ponctuation et chiffres")
            logger.info("✅ Suppression mots vides (stopwords)")
            logger.info("✅ Lemmatisation appliquée")
        else:
            logger.info("✅ ÉTAPE 2 DÉJÀ COMPLÈTE - Tous les documents sont prétraités")
        
    except KeyboardInterrupt:
        logger.info("Prétraitement interrompu par l'utilisateur")
    except Exception as e:
        logger.error(f"Erreur: {e}")
    finally:
        if preprocesseur:
            preprocesseur.fermer_connexions()

if __name__ == "__main__":
    main()
