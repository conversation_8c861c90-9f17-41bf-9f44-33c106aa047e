{"metadata": {"date_rapport": "2025-06-07T11:58:49.253801", "duree_export_secondes": 2.49703049659729, "version": "1.0.0"}, "resultats": {"exports_realises": ["MongoDB", "Elasticsearch"], "exports_echoues": [], "taux_reussite": 100.0}, "fichiers": {"total_fichiers": 12, "taille_totale_kb": 2511.27, "statistiques_extensions": {".json": {"count": 8, "taille_totale_kb": 1732.99}, ".ndjson": {"count": 1, "taille_totale_kb": 338.27}, ".csv": {"count": 2, "taille_totale_kb": 408.89000000000004}, ".xlsx": {"count": 1, "taille_totale_kb": 31.12}}, "liste_fichiers": [{"nom": "elasticsearch_agregations_20250607_115849.json", "taille_kb": 1.7, "date_creation": "2025-06-07T11:58:49.209865", "extension": ".json"}, {"nom": "elasticsearch_bulk_20250607_115849.ndjson", "taille_kb": 338.27, "date_creation": "2025-06-07T11:58:49.178329", "extension": ".nd<PERSON><PERSON>"}, {"nom": "elasticsearch_export_20250607_115849.csv", "taille_kb": 63.17, "date_creation": "2025-06-07T11:58:49.198351", "extension": ".csv"}, {"nom": "elasticsearch_export_20250607_115849.json", "taille_kb": 398.5, "date_creation": "2025-06-07T11:58:49.160166", "extension": ".json"}, {"nom": "elasticsearch_mapping_20250607_115849.json", "taille_kb": 1.99, "date_creation": "2025-06-07T11:58:49.218186", "extension": ".json"}, {"nom": "elasticsearch_requetes_20250607_115849.json", "taille_kb": 2.94, "date_creation": "2025-06-07T11:58:49.224107", "extension": ".json"}, {"nom": "mongodb_echantillon_50_20250607_115849.json", "taille_kb": 149.03, "date_creation": "2025-06-07T11:58:49.086151", "extension": ".json"}, {"nom": "mongodb_export_20250607_115848.csv", "taille_kb": 345.72, "date_creation": "2025-06-07T11:58:48.478429", "extension": ".csv"}, {"nom": "mongodb_export_20250607_115848.xlsx", "taille_kb": 31.12, "date_creation": "2025-06-07T11:58:49.038789", "extension": ".xlsx"}, {"nom": "mongodb_export_analyses_20250607_115848.json", "taille_kb": 502.79, "date_creation": "2025-06-07T11:58:48.452389", "extension": ".json"}, {"nom": "mongodb_export_complet_20250607_115848.json", "taille_kb": 674.46, "date_creation": "2025-06-07T11:58:48.415302", "extension": ".json"}, {"nom": "mongodb_statistiques_20250607_115849.json", "taille_kb": 1.58, "date_creation": "2025-06-07T11:58:49.062843", "extension": ".json"}]}, "recommandations": {"formats_recommandes": {"analyse_rapide": "CSV (facile à ouvrir dans Excel)", "integration_systeme": "JSON (format standard)", "import_elasticsearch": "NDJSON (format bulk)", "presentation": "Excel (graphiques intégrés)"}, "utilisation": {"recherche": "Utiliser les fichiers JSON avec requêtes exemples", "visualisation": "Importer CSV dans Tableau/PowerBI", "backup": "Conserver JSON complet", "partage": "Utiliser échantillons pour démonstration"}}}