#!/usr/bin/env python3
"""
Export des données MongoDB
Exporte les données de harcèlement depuis MongoDB vers différents formats
"""

import json
import csv
import logging
from datetime import datetime
from pathlib import Path
import pandas as pd
from pymongo import MongoClient

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MongoDBExporter:
    def __init__(self, host="localhost", port=27017, database="harcelement", collection="posts"):
        """Initialise l'exporteur MongoDB"""
        self.mongo_client = MongoClient(f"mongodb://{host}:{port}/")
        self.db = self.mongo_client[database]
        self.collection = self.db[collection]
        
        # Créer le dossier d'export
        self.export_dir = Path("exports")
        self.export_dir.mkdir(exist_ok=True)
        
        logger.info(f"🔗 Connexion MongoDB: {host}:{port}/{database}.{collection}")
        logger.info(f"📁 Dossier d'export: {self.export_dir}")
    
    def get_export_stats(self):
        """Obtient les statistiques des données à exporter"""
        total_docs = self.collection.count_documents({})
        processed_docs = self.collection.count_documents({"nlp_effectue": True})
        
        # Statistiques par étape
        stats = {
            "total_documents": total_docs,
            "documents_collectes": self.collection.count_documents({"collecte_effectuee": True}),
            "documents_pretraites": self.collection.count_documents({"pretraitement_effectue": True}),
            "documents_analyses": processed_docs,
            "documents_bruts": total_docs - processed_docs
        }
        
        # Répartition par plateforme
        plateformes = list(self.collection.aggregate([
            {"$group": {"_id": "$plateforme", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]))
        
        # Répartition par sentiment
        sentiments = list(self.collection.aggregate([
            {"$match": {"nlp_effectue": True}},
            {"$group": {"_id": "$sentiment", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]))
        
        # Répartition par langue
        langues = list(self.collection.aggregate([
            {"$match": {"nlp_effectue": True}},
            {"$group": {"_id": "$langue_nom", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]))
        
        stats.update({
            "plateformes": plateformes,
            "sentiments": sentiments,
            "langues": langues
        })
        
        return stats
    
    def export_json_complet(self, filename=None):
        """Exporte toutes les données en JSON"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mongodb_export_complet_{timestamp}.json"
        
        filepath = self.export_dir / filename
        
        logger.info(f"📄 Export JSON complet vers: {filepath}")
        
        # Récupérer tous les documents
        cursor = self.collection.find({})
        documents = []
        
        for doc in cursor:
            # Convertir ObjectId en string
            doc["_id"] = str(doc["_id"])
            # Convertir les dates en ISO format
            for key, value in doc.items():
                if isinstance(value, datetime):
                    doc[key] = value.isoformat()
            documents.append(doc)
        
        # Sauvegarder
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump({
                "metadata": {
                    "export_date": datetime.now().isoformat(),
                    "total_documents": len(documents),
                    "source": "MongoDB harcelement.posts"
                },
                "documents": documents
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Export JSON terminé: {len(documents)} documents")
        return filepath
    
    def export_json_analyses(self, filename=None):
        """Exporte uniquement les données analysées en JSON"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mongodb_export_analyses_{timestamp}.json"
        
        filepath = self.export_dir / filename
        
        logger.info(f"📄 Export JSON analyses vers: {filepath}")
        
        # Récupérer les documents analysés
        cursor = self.collection.find({"nlp_effectue": True})
        documents = []
        
        for doc in cursor:
            # Sélectionner les champs pertinents
            export_doc = {
                "id": str(doc["_id"]),
                "titre": doc.get("titre", ""),
                "contenu": doc.get("contenu", ""),
                "auteur": doc.get("auteur", ""),
                "date": doc.get("date").isoformat() if doc.get("date") else None,
                "url": doc.get("url", ""),
                "plateforme": doc.get("plateforme", ""),
                "source": doc.get("source", ""),
                
                # Données de prétraitement
                "langue_code": doc.get("langue_code", ""),
                "langue_nom": doc.get("langue_nom", ""),
                "texte_pretraite": doc.get("texte_complet_pretraite", ""),
                
                # Données d'analyse NLP
                "sentiment": doc.get("sentiment", ""),
                "sentiment_score": doc.get("sentiment_score", 0),
                "sentiment_vader": doc.get("sentiment_vader", {}),
                "sentiment_textblob": doc.get("sentiment_textblob", {}),
                
                # Métadonnées
                "date_collecte": doc.get("date_collecte").isoformat() if doc.get("date_collecte") else None,
                "date_analyse": doc.get("date_analyse_nlp").isoformat() if doc.get("date_analyse_nlp") else None
            }
            documents.append(export_doc)
        
        # Sauvegarder
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump({
                "metadata": {
                    "export_date": datetime.now().isoformat(),
                    "total_documents": len(documents),
                    "source": "MongoDB harcelement.posts (analysés)",
                    "fields": list(documents[0].keys()) if documents else []
                },
                "documents": documents
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Export JSON analyses terminé: {len(documents)} documents")
        return filepath
    
    def export_csv(self, filename=None, include_content=True):
        """Exporte les données analysées en CSV"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mongodb_export_{timestamp}.csv"
        
        filepath = self.export_dir / filename
        
        logger.info(f"📊 Export CSV vers: {filepath}")
        
        # Récupérer les documents analysés
        cursor = self.collection.find({"nlp_effectue": True})
        
        # Préparer les données pour CSV
        csv_data = []
        for doc in cursor:
            row = {
                "id": str(doc["_id"]),
                "titre": doc.get("titre", ""),
                "auteur": doc.get("auteur", ""),
                "date": doc.get("date").strftime("%Y-%m-%d %H:%M:%S") if doc.get("date") else "",
                "plateforme": doc.get("plateforme", ""),
                "source": doc.get("source", ""),
                "langue": doc.get("langue_nom", ""),
                "sentiment": doc.get("sentiment", ""),
                "sentiment_score": doc.get("sentiment_score", 0),
                "vader_compound": doc.get("sentiment_vader", {}).get("compound", 0),
                "textblob_polarity": doc.get("sentiment_textblob", {}).get("polarity", 0),
                "url": doc.get("url", "")
            }
            
            # Inclure le contenu si demandé
            if include_content:
                row["contenu"] = doc.get("contenu", "")
                row["contenu_pretraite"] = doc.get("texte_complet_pretraite", "")
            
            csv_data.append(row)
        
        # Sauvegarder en CSV
        if csv_data:
            fieldnames = csv_data[0].keys()
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(csv_data)
        
        logger.info(f"✅ Export CSV terminé: {len(csv_data)} documents")
        return filepath
    
    def export_excel(self, filename=None):
        """Exporte les données en Excel avec plusieurs feuilles"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mongodb_export_{timestamp}.xlsx"
        
        filepath = self.export_dir / filename
        
        logger.info(f"📈 Export Excel vers: {filepath}")
        
        # Créer un writer Excel
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            
            # Feuille 1: Données principales
            cursor = self.collection.find({"nlp_effectue": True})
            main_data = []
            
            for doc in cursor:
                row = {
                    "ID": str(doc["_id"]),
                    "Titre": doc.get("titre", ""),
                    "Auteur": doc.get("auteur", ""),
                    "Date": doc.get("date"),
                    "Plateforme": doc.get("plateforme", ""),
                    "Source": doc.get("source", ""),
                    "Langue": doc.get("langue_nom", ""),
                    "Sentiment": doc.get("sentiment", ""),
                    "Score_Sentiment": doc.get("sentiment_score", 0),
                    "Score_VADER": doc.get("sentiment_vader", {}).get("compound", 0),
                    "Score_TextBlob": doc.get("sentiment_textblob", {}).get("polarity", 0),
                    "URL": doc.get("url", "")
                }
                main_data.append(row)
            
            df_main = pd.DataFrame(main_data)
            df_main.to_excel(writer, sheet_name='Données_Principales', index=False)
            
            # Feuille 2: Statistiques par plateforme
            plateformes_stats = list(self.collection.aggregate([
                {"$match": {"nlp_effectue": True}},
                {"$group": {
                    "_id": "$plateforme",
                    "total": {"$sum": 1},
                    "negatif": {"$sum": {"$cond": [{"$eq": ["$sentiment", "négatif"]}, 1, 0]}},
                    "neutre": {"$sum": {"$cond": [{"$eq": ["$sentiment", "neutre"]}, 1, 0]}},
                    "positif": {"$sum": {"$cond": [{"$eq": ["$sentiment", "positif"]}, 1, 0]}},
                    "score_moyen": {"$avg": "$sentiment_score"}
                }}
            ]))
            
            df_plateformes = pd.DataFrame([
                {
                    "Plateforme": stat["_id"],
                    "Total": stat["total"],
                    "Négatif": stat["negatif"],
                    "Neutre": stat["neutre"],
                    "Positif": stat["positif"],
                    "Score_Moyen": round(stat["score_moyen"], 3)
                }
                for stat in plateformes_stats
            ])
            df_plateformes.to_excel(writer, sheet_name='Stats_Plateformes', index=False)
            
            # Feuille 3: Statistiques par langue
            langues_stats = list(self.collection.aggregate([
                {"$match": {"nlp_effectue": True}},
                {"$group": {
                    "_id": "$langue_nom",
                    "total": {"$sum": 1},
                    "score_moyen": {"$avg": "$sentiment_score"}
                }},
                {"$sort": {"total": -1}}
            ]))
            
            df_langues = pd.DataFrame([
                {
                    "Langue": stat["_id"],
                    "Total": stat["total"],
                    "Score_Moyen": round(stat["score_moyen"], 3)
                }
                for stat in langues_stats
            ])
            df_langues.to_excel(writer, sheet_name='Stats_Langues', index=False)
            
            # Feuille 4: Top contenus négatifs
            cursor_negatifs = self.collection.find(
                {"nlp_effectue": True, "sentiment": "négatif"},
                {"titre": 1, "auteur": 1, "sentiment_score": 1, "plateforme": 1, "date": 1}
            ).sort("sentiment_score", 1).limit(20)
            
            negatifs_data = []
            for doc in cursor_negatifs:
                negatifs_data.append({
                    "Titre": doc.get("titre", "")[:100] + "..." if len(doc.get("titre", "")) > 100 else doc.get("titre", ""),
                    "Auteur": doc.get("auteur", ""),
                    "Score": doc.get("sentiment_score", 0),
                    "Plateforme": doc.get("plateforme", ""),
                    "Date": doc.get("date")
                })
            
            df_negatifs = pd.DataFrame(negatifs_data)
            df_negatifs.to_excel(writer, sheet_name='Top_Contenus_Négatifs', index=False)
        
        logger.info(f"✅ Export Excel terminé: 4 feuilles créées")
        return filepath
    
    def export_statistiques_json(self, filename=None):
        """Exporte les statistiques détaillées en JSON"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mongodb_statistiques_{timestamp}.json"
        
        filepath = self.export_dir / filename
        
        logger.info(f"📊 Export statistiques vers: {filepath}")
        
        stats = self.get_export_stats()
        
        # Ajouter des statistiques avancées
        # Distribution des scores de sentiment
        pipeline_scores = [
            {"$match": {"nlp_effectue": True, "sentiment_score": {"$exists": True}}},
            {"$bucket": {
                "groupBy": "$sentiment_score",
                "boundaries": [-1, -0.5, -0.1, 0.1, 0.5, 1],
                "default": "other",
                "output": {"count": {"$sum": 1}}
            }}
        ]
        
        distribution_scores = list(self.collection.aggregate(pipeline_scores))
        
        # Évolution temporelle
        pipeline_temporel = [
            {"$match": {"nlp_effectue": True, "date": {"$exists": True}}},
            {"$group": {
                "_id": {"$dateToString": {"format": "%Y-%m", "date": "$date"}},
                "count": {"$sum": 1},
                "score_moyen": {"$avg": "$sentiment_score"}
            }},
            {"$sort": {"_id": 1}}
        ]
        
        evolution_temporelle = list(self.collection.aggregate(pipeline_temporel))
        
        stats.update({
            "distribution_scores": distribution_scores,
            "evolution_temporelle": evolution_temporelle,
            "export_metadata": {
                "date_export": datetime.now().isoformat(),
                "version": "1.0.0",
                "source": "MongoDB harcelement.posts"
            }
        })
        
        # Sauvegarder
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"✅ Export statistiques terminé")
        return filepath
    
    def export_echantillon(self, taille=50, filename=None):
        """Exporte un échantillon représentatif des données"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mongodb_echantillon_{taille}_{timestamp}.json"
        
        filepath = self.export_dir / filename
        
        logger.info(f"🎲 Export échantillon ({taille} documents) vers: {filepath}")
        
        # Échantillon stratifié par sentiment
        echantillon = []
        
        for sentiment in ["négatif", "neutre", "positif"]:
            taille_sentiment = taille // 3
            cursor = self.collection.aggregate([
                {"$match": {"nlp_effectue": True, "sentiment": sentiment}},
                {"$sample": {"size": taille_sentiment}}
            ])
            
            for doc in cursor:
                doc["_id"] = str(doc["_id"])
                # Convertir les dates
                for key, value in doc.items():
                    if isinstance(value, datetime):
                        doc[key] = value.isoformat()
                echantillon.append(doc)
        
        # Sauvegarder
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump({
                "metadata": {
                    "export_date": datetime.now().isoformat(),
                    "taille_echantillon": len(echantillon),
                    "methode": "échantillonnage stratifié par sentiment",
                    "source": "MongoDB harcelement.posts"
                },
                "echantillon": echantillon
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Export échantillon terminé: {len(echantillon)} documents")
        return filepath
    
    def export_tout(self):
        """Exporte toutes les données dans tous les formats"""
        logger.info("🚀 EXPORT COMPLET MONGODB")
        logger.info("=" * 50)
        
        # Statistiques avant export
        stats = self.get_export_stats()
        logger.info(f"📊 Documents à exporter: {stats['total_documents']}")
        logger.info(f"📊 Documents analysés: {stats['documents_analyses']}")
        
        exports_realises = []
        
        try:
            # 1. JSON complet
            logger.info("\n1️⃣ Export JSON complet...")
            filepath = self.export_json_complet()
            exports_realises.append(("JSON Complet", filepath))
            
            # 2. JSON analyses uniquement
            logger.info("\n2️⃣ Export JSON analyses...")
            filepath = self.export_json_analyses()
            exports_realises.append(("JSON Analyses", filepath))
            
            # 3. CSV
            logger.info("\n3️⃣ Export CSV...")
            filepath = self.export_csv()
            exports_realises.append(("CSV", filepath))
            
            # 4. Excel
            logger.info("\n4️⃣ Export Excel...")
            filepath = self.export_excel()
            exports_realises.append(("Excel", filepath))
            
            # 5. Statistiques
            logger.info("\n5️⃣ Export statistiques...")
            filepath = self.export_statistiques_json()
            exports_realises.append(("Statistiques", filepath))
            
            # 6. Échantillon
            logger.info("\n6️⃣ Export échantillon...")
            filepath = self.export_echantillon()
            exports_realises.append(("Échantillon", filepath))
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'export: {e}")
            return False
        
        # Résumé
        logger.info("\n" + "=" * 50)
        logger.info("📋 RÉSUMÉ DES EXPORTS MONGODB")
        logger.info("=" * 50)
        
        for nom, filepath in exports_realises:
            taille = filepath.stat().st_size / 1024  # KB
            logger.info(f"✅ {nom:<20} : {filepath.name} ({taille:.1f} KB)")
        
        logger.info(f"\n📁 Tous les fichiers dans: {self.export_dir}")
        logger.info(f"🎉 {len(exports_realises)} exports réalisés avec succès !")
        
        return True
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("🔌 Connexions MongoDB fermées")

def main():
    """Fonction principale"""
    exporter = None
    
    try:
        # Initialiser l'exporteur
        exporter = MongoDBExporter()
        
        # Exporter toutes les données
        success = exporter.export_tout()
        
        if success:
            logger.info("\n✅ EXPORT MONGODB TERMINÉ AVEC SUCCÈS !")
        else:
            logger.error("\n❌ ERREURS LORS DE L'EXPORT MONGODB")
        
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
    finally:
        if exporter:
            exporter.fermer_connexions()

if __name__ == "__main__":
    main()
