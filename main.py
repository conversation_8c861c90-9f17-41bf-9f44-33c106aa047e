#!/usr/bin/env python3
"""
Main orchestrator for social media harassment data collection
Supports Reddit, Twitter, and Telegram scraping
"""

import logging
import sys
import time
from datetime import datetime
from typing import Dict, Any

from config import Config
from database import DatabaseManager
from scraper import RedditScraper
from twitter_scraper import TwitterScraper
from telegram_scraper import TelegramScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('harassment_scraper.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class HarassmentDataCollector:
    """Main class for orchestrating social media data collection"""
    
    def __init__(self):
        self.config = Config()
        self.db_manager = None
        self.reddit_scraper = None
        self.twitter_scraper = None
        self.telegram_scraper = None
        
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all components"""
        logger.info("Initializing Harassment Data Collector...")
        
        # Validate configuration
        if not self.config.validate_config():
            logger.error("Configuration validation failed")
            sys.exit(1)
        
        # Initialize database
        try:
            self.db_manager = DatabaseManager(self.config)
            logger.info("Database manager initialized")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            sys.exit(1)
        
        # Initialize scrapers
        self.reddit_scraper = RedditScraper(self.config, self.db_manager)
        self.twitter_scraper = TwitterScraper(self.config, self.db_manager)
        self.telegram_scraper = TelegramScraper(self.config, self.db_manager)
        
        logger.info("All components initialized successfully")
    
    def collect_reddit_data(self) -> Dict[str, int]:
        """Collect data from Reddit"""
        logger.info("=" * 50)
        logger.info("Starting Reddit data collection")
        logger.info("=" * 50)
        
        start_time = time.time()
        results = self.reddit_scraper.scrape_all_subreddits()
        end_time = time.time()
        
        total_posts = sum(results.values())
        logger.info(f"Reddit collection completed in {end_time - start_time:.2f} seconds")
        logger.info(f"Total new posts collected: {total_posts}")
        
        for subreddit, count in results.items():
            logger.info(f"  r/{subreddit}: {count} new posts")
        
        return results
    
    def collect_twitter_data(self) -> Dict[str, int]:
        """Collect data from Twitter"""
        logger.info("=" * 50)
        logger.info("Starting Twitter data collection")
        logger.info("=" * 50)
        
        if not self.twitter_scraper.is_available():
            logger.warning("Twitter scraper not available - skipping")
            return {}
        
        start_time = time.time()
        results = self.twitter_scraper.search_harassment_keywords()
        end_time = time.time()
        
        total_tweets = sum(results.values())
        logger.info(f"Twitter collection completed in {end_time - start_time:.2f} seconds")
        logger.info(f"Total new tweets collected: {total_tweets}")
        
        for keyword, count in results.items():
            logger.info(f"  '{keyword}': {count} new tweets")
        
        return results
    
    def collect_telegram_data(self) -> Dict[str, int]:
        """Collect data from Telegram"""
        logger.info("=" * 50)
        logger.info("Starting Telegram data collection")
        logger.info("=" * 50)
        
        if not self.telegram_scraper.is_available():
            logger.warning("Telegram scraper not available - skipping")
            return {}
        
        start_time = time.time()
        results = self.telegram_scraper.run_scraping()
        end_time = time.time()
        
        total_messages = sum(results.values())
        logger.info(f"Telegram collection completed in {end_time - start_time:.2f} seconds")
        logger.info(f"Total new messages collected: {total_messages}")
        
        for channel, count in results.items():
            logger.info(f"  {channel}: {count} new messages")
        
        return results
    
    def collect_all_data(self) -> Dict[str, Any]:
        """Collect data from all platforms"""
        logger.info("🚀 Starting comprehensive harassment data collection")
        logger.info(f"Collection started at: {datetime.now()}")
        
        overall_start_time = time.time()
        results = {
            "reddit": {},
            "twitter": {},
            "telegram": {},
            "summary": {}
        }
        
        # Collect from Reddit
        try:
            results["reddit"] = self.collect_reddit_data()
        except Exception as e:
            logger.error(f"Reddit collection failed: {e}")
            results["reddit"] = {}
        
        # Wait between platforms
        time.sleep(self.config.RATE_LIMIT_DELAY)
        
        # Collect from Twitter
        try:
            results["twitter"] = self.collect_twitter_data()
        except Exception as e:
            logger.error(f"Twitter collection failed: {e}")
            results["twitter"] = {}
        
        # Wait between platforms
        time.sleep(self.config.RATE_LIMIT_DELAY)
        
        # Collect from Telegram
        try:
            results["telegram"] = self.collect_telegram_data()
        except Exception as e:
            logger.error(f"Telegram collection failed: {e}")
            results["telegram"] = {}
        
        # Calculate summary
        overall_end_time = time.time()
        total_duration = overall_end_time - overall_start_time
        
        results["summary"] = {
            "total_reddit_posts": sum(results["reddit"].values()),
            "total_twitter_tweets": sum(results["twitter"].values()),
            "total_telegram_messages": sum(results["telegram"].values()),
            "total_duration_seconds": total_duration,
            "collection_timestamp": datetime.now().isoformat()
        }
        
        # Log final summary
        self._log_final_summary(results)
        
        return results
    
    def _log_final_summary(self, results: Dict[str, Any]):
        """Log final collection summary"""
        logger.info("=" * 60)
        logger.info("📊 FINAL COLLECTION SUMMARY")
        logger.info("=" * 60)
        
        summary = results["summary"]
        logger.info(f"Reddit posts: {summary['total_reddit_posts']}")
        logger.info(f"Twitter tweets: {summary['total_twitter_tweets']}")
        logger.info(f"Telegram messages: {summary['total_telegram_messages']}")
        logger.info(f"Total items: {summary['total_reddit_posts'] + summary['total_twitter_tweets'] + summary['total_telegram_messages']}")
        logger.info(f"Total duration: {summary['total_duration_seconds']:.2f} seconds")
        
        # Database statistics
        try:
            db_stats = self.db_manager.get_statistics()
            logger.info(f"Database total posts: {db_stats.get('total_posts', 0)}")
            logger.info("Platform distribution:")
            for platform, count in db_stats.get('platforms', {}).items():
                logger.info(f"  {platform}: {count}")
        except Exception as e:
            logger.error(f"Error getting database statistics: {e}")
        
        logger.info("=" * 60)
        logger.info("✅ Collection completed successfully!")
        logger.info("=" * 60)
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get current database statistics"""
        return self.db_manager.get_statistics()
    
    def cleanup(self):
        """Cleanup resources"""
        if self.db_manager:
            self.db_manager.close_connection()
        logger.info("Cleanup completed")

def main():
    """Main entry point"""
    collector = None
    
    try:
        collector = HarassmentDataCollector()
        results = collector.collect_all_data()
        
        # Optionally save results to file
        import json
        with open(f"collection_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Collection interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1
    finally:
        if collector:
            collector.cleanup()

if __name__ == "__main__":
    sys.exit(main())
