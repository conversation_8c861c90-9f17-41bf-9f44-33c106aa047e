import praw
import logging
from datetime import datetime, timezone
import time
from typing import Dict, List, Optional
from config import Config
from database import DatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RedditScraper:
    def __init__(self, config: Config, db_manager: DatabaseManager):
        self.config = config
        self.db_manager = db_manager
        self.reddit = praw.Reddit(
            client_id=config.REDDIT_CLIENT_ID,
            client_secret=config.REDDIT_CLIENT_SECRET,
            user_agent=config.REDDIT_USER_AGENT
        )
        logger.info("Reddit scraper initialized")

    def scrape_subreddit(self, subreddit_name: str, limit: int = 100) -> int:
        """Scrape posts from a specific subreddit"""
        logger.info(f"Starting to scrape r/{subreddit_name} with limit {limit}")
        posts_added = 0

        try:
            subreddit = self.reddit.subreddit(subreddit_name)

            for post in subreddit.hot(limit=limit):
                if post.stickied:
                    continue  # Skip pinned posts

                post_data = self._extract_post_data(post, subreddit_name)

                if self.db_manager.insert_post(post_data):
                    posts_added += 1
                    logger.info(f"Added post: {post.title[:60]}...")
                else:
                    logger.debug(f"Post already exists: {post.title[:60]}...")

        except Exception as e:
            logger.error(f"Error scraping r/{subreddit_name}: {str(e)}")

        logger.info(f"Finished scraping r/{subreddit_name}. Added {posts_added} new posts")
        return posts_added

    def _extract_post_data(self, post, subreddit_name: str) -> Dict:
        """Extract relevant data from a Reddit post"""
        return {
            "title": post.title,
            "content": post.selftext,
            "author": str(post.author) if post.author else "[deleted]",
            "date": datetime.fromtimestamp(post.created_utc, timezone.utc),
            "url": post.url,
            "source": f"r/{subreddit_name}",
            "platform": "reddit",
            "post_id": post.id,
            "score": post.score,
            "num_comments": post.num_comments
        }

    def scrape_all_subreddits(self) -> Dict[str, int]:
        """Scrape all configured subreddits"""
        results = {}

        for subreddit_name in self.config.REDDIT_SUBREDDITS:
            posts_added = self.scrape_subreddit(subreddit_name, self.config.REDDIT_LIMIT_POSTS)
            results[subreddit_name] = posts_added
            time.sleep(2)  # Rate limiting

        return results


if __name__ == "__main__":
    # This will be moved to main.py, keeping for backward compatibility
    from config import Config
    from database import DatabaseManager

    config = Config()
    db_manager = DatabaseManager(config)
    scraper = RedditScraper(config, db_manager)

    results = scraper.scrape_all_subreddits()

    total_posts = sum(results.values())
    logger.info(f"✅ Reddit scraping completed. Total new posts: {total_posts}")

    for subreddit, count in results.items():
        logger.info(f"  r/{subreddit}: {count} new posts")
