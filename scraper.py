import praw
from pymongo import MongoClient
from datetime import datetime
import time

# === Configuration Reddit API ===
reddit = praw.Reddit(
    client_id="naoE5J20fwcnusZT_C0vkA",
    client_secret="2827ZUySYcMwUp0TfyA8JG5cTvI7cw",
    user_agent="harcelement script by /u/Fit-Juice-9025"
)

# === Configuration MongoDB ===
mongo_client = MongoClient("mongodb://localhost:27017/")
db = mongo_client["harcelement"]
collection = db["posts"]

# === Subreddits à scraper ===
subreddits = ["bullying", "TrueOffMyChest"]
limit_posts = 100  # nombre de posts par subreddit

def scrape_subreddit(subreddit_name):
    print(f"Scraping r/{subreddit_name}...")
    subreddit = reddit.subreddit(subreddit_name)

    for post in subreddit.hot(limit=limit_posts):
        if post.stickied:
            continue  # ignorer les posts épinglés

        post_data = {
            "title": post.title,
            "content": post.selftext,
            "author": str(post.author),
            "date": datetime.utcfromtimestamp(post.created_utc),
            "url": post.url,
            "source": f"r/{subreddit_name}",
        }

        # Vérifie si le post existe déjà (évite les doublons)
        if collection.count_documents({"url": post.url}) == 0:
            collection.insert_one(post_data)
            print(f"[+] Post ajouté : {post.title[:60]}...")
        else:
            print(f"[=] Post déjà existant : {post.title[:60]}")

if __name__ == "__main__":
    for subreddit_name in subreddits:
        scrape_subreddit(subreddit_name)
        time.sleep(2)  # petite pause pour éviter de spammer l'API
    print("✅ Scraping terminé.")
