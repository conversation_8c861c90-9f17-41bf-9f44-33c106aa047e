#!/usr/bin/env python3
"""
ÉTAPE 4 - Indexation Elasticsearch (Mode Simulation)
Simule l'indexation des données enrichies de MongoDB vers Elasticsearch
Démontre la structure et le processus d'indexation
"""

import logging
from pymongo import MongoClient
from datetime import datetime
import json

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IndexeurElasticsearchSimulation:
    def __init__(self):
        """Initialise l'indexeur Elasticsearch en mode simulation"""
        # Configuration MongoDB
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]
        
        # Nom de l'index
        self.index_name = "harcelement_posts"
        
        # Documents simulés
        self.documents_simules = []
        
        logger.info("Indexeur Elasticsearch initialisé (MODE SIMULATION)")
        logger.info(f"Index cible: {self.index_name}")
        logger.info("⚠️ Elasticsearch non disponible - Mode simulation activé")
    
    def creer_mapping_index_simulation(self):
        """Affiche le mapping qui serait créé pour l'index"""
        mapping = {
            "mappings": {
                "properties": {
                    # Champs principaux requis
                    "titre": {"type": "text", "analyzer": "standard"},
                    "contenu": {"type": "text", "analyzer": "standard"},
                    "auteur": {"type": "keyword"},
                    "date": {"type": "date"},
                    "url": {"type": "keyword", "index": False},
                    "langue": {"type": "keyword"},
                    "sentiment": {"type": "keyword"},
                    "score": {"type": "float"},
                    
                    # Champs supplémentaires
                    "plateforme": {"type": "keyword"},
                    "source": {"type": "keyword"},
                    "sentiment_vader": {
                        "properties": {
                            "sentiment": {"type": "keyword"},
                            "compound": {"type": "float"},
                            "positif": {"type": "float"},
                            "neutre": {"type": "float"},
                            "negatif": {"type": "float"}
                        }
                    }
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }
        
        logger.info("🔄 Simulation: Mapping de l'index Elasticsearch")
        logger.info(f"Structure de l'index '{self.index_name}':")
        logger.info("  - titre: text (analysé)")
        logger.info("  - contenu: text (analysé)")
        logger.info("  - auteur: keyword")
        logger.info("  - date: date")
        logger.info("  - url: keyword (non indexé)")
        logger.info("  - langue: keyword")
        logger.info("  - sentiment: keyword")
        logger.info("  - score: float")
        logger.info("  - plateforme: keyword")
        logger.info("  - source: keyword")
        logger.info("  - sentiment_vader: object (détails sentiment)")
        
        return mapping
    
    def convertir_document_mongodb_vers_es(self, doc_mongo):
        """Convertit un document MongoDB vers le format Elasticsearch"""
        try:
            # Document de base pour Elasticsearch avec champs requis
            doc_es = {
                # Champs principaux requis par l'énoncé
                "titre": doc_mongo.get("titre", ""),
                "contenu": doc_mongo.get("contenu", ""),
                "auteur": doc_mongo.get("auteur", ""),
                "date": doc_mongo.get("date"),
                "url": doc_mongo.get("url", ""),
                "langue": doc_mongo.get("langue_code", ""),
                "sentiment": doc_mongo.get("sentiment", ""),
                "score": doc_mongo.get("sentiment_score", 0.0),
                
                # Champs supplémentaires pour enrichissement
                "langue_nom": doc_mongo.get("langue_nom", ""),
                "plateforme": doc_mongo.get("plateforme", ""),
                "source": doc_mongo.get("source", ""),
                "post_id": doc_mongo.get("post_id", ""),
                
                # Champs prétraités
                "titre_pretraite": doc_mongo.get("titre_pretraite", ""),
                "contenu_pretraite": doc_mongo.get("contenu_pretraite", ""),
                "texte_complet_pretraite": doc_mongo.get("texte_complet_pretraite", ""),
                
                # Détails sentiment
                "sentiment_vader": doc_mongo.get("sentiment_vader", {}),
                "sentiment_textblob": doc_mongo.get("sentiment_textblob", {}),
                
                # Métadonnées
                "date_collecte": doc_mongo.get("date_collecte"),
                "date_pretraitement": doc_mongo.get("date_pretraitement"),
                "date_analyse_nlp": doc_mongo.get("date_analyse_nlp"),
                "date_indexation": datetime.now()
            }
            
            # Nettoyer les valeurs None
            doc_es = {k: v for k, v in doc_es.items() if v is not None}
            
            return doc_es
            
        except Exception as e:
            logger.error(f"Erreur conversion document {doc_mongo.get('_id', 'unknown')}: {e}")
            return None
    
    def simuler_indexation(self):
        """Simule l'indexation des documents"""
        logger.info("=" * 60)
        logger.info("DÉBUT INDEXATION ELASTICSEARCH - ÉTAPE 4 (SIMULATION)")
        logger.info("=" * 60)
        
        # Compter les documents à indexer
        total_documents = self.collection.count_documents({"nlp_effectue": True})
        logger.info(f"Documents à indexer: {total_documents}")
        
        if total_documents == 0:
            logger.warning("Aucun document analysé trouvé dans MongoDB")
            return 0
        
        logger.info("🔄 Mode simulation - Indexation des documents...")
        
        # Récupérer et convertir les documents
        cursor = self.collection.find({"nlp_effectue": True})
        documents_simules = 0
        
        for doc_mongo in cursor:
            doc_es = self.convertir_document_mongodb_vers_es(doc_mongo)
            if doc_es:
                # Ajouter à la liste simulée
                self.documents_simules.append({
                    "id": str(doc_mongo["_id"]),
                    "document": doc_es
                })
                documents_simules += 1
                
                if documents_simules % 50 == 0:
                    logger.info(f"🔄 Simulés: {documents_simules} documents")
        
        logger.info(f"✅ Simulation terminée: {documents_simules} documents indexés")
        return documents_simules
    
    def verifier_indexation_simulation(self):
        """Vérifie la simulation d'indexation"""
        count_es = len(self.documents_simules)
        count_mongo = self.collection.count_documents({"nlp_effectue": True})
        
        logger.info(f"\n📊 VÉRIFICATION INDEXATION (SIMULATION)")
        logger.info(f"Documents dans MongoDB: {count_mongo}")
        logger.info(f"Documents simulés dans Elasticsearch: {count_es}")
        logger.info(f"Taux de réussite: {(count_es/count_mongo*100):.1f}%" if count_mongo > 0 else "0%")
        
        return count_es == count_mongo
    
    def afficher_exemples_simulation(self, limite=3):
        """Affiche des exemples de documents simulés"""
        logger.info(f"\n🔍 EXEMPLES DOCUMENTS INDEXÉS (SIMULATION)")
        logger.info("-" * 60)
        
        for i, doc_sim in enumerate(self.documents_simules[:limite], 1):
            doc = doc_sim['document']
            logger.info(f"\nExemple {i}:")
            logger.info(f"  ID: {doc_sim['id']}")
            logger.info(f"  Titre: {doc.get('titre', '')[:60]}...")
            logger.info(f"  Auteur: {doc.get('auteur', 'N/A')}")
            logger.info(f"  Langue: {doc.get('langue_nom', 'N/A')} ({doc.get('langue', 'N/A')})")
            logger.info(f"  Sentiment: {doc.get('sentiment', 'N/A')} (score: {doc.get('score', 0):.3f})")
            logger.info(f"  Plateforme: {doc.get('plateforme', 'N/A')}")
            logger.info(f"  URL: {doc.get('url', 'N/A')[:50]}...")
            
            # Afficher les champs requis spécifiquement
            logger.info(f"  ✅ Champs requis présents:")
            champs_requis = ['titre', 'contenu', 'auteur', 'date', 'url', 'langue', 'sentiment', 'score']
            for champ in champs_requis:
                valeur = doc.get(champ, 'MANQUANT')
                if valeur != 'MANQUANT' and valeur is not None:
                    logger.info(f"     ✓ {champ}: OK")
                else:
                    logger.info(f"     ✗ {champ}: MANQUANT")
    
    def obtenir_statistiques_simulation(self):
        """Obtient les statistiques de la simulation"""
        total_docs = len(self.documents_simules)
        
        logger.info(f"\n📊 STATISTIQUES ELASTICSEARCH (SIMULATION)")
        logger.info(f"Index: {self.index_name}")
        logger.info(f"Total documents: {total_docs}")
        logger.info(f"Taille estimée: {total_docs * 2:.2f} KB")
        
        # Agrégations simulées
        stats_sentiment = {}
        stats_langue = {}
        stats_plateforme = {}
        
        for doc_sim in self.documents_simules:
            doc = doc_sim['document']
            
            # Compter par sentiment
            sentiment = doc.get('sentiment', 'inconnu')
            stats_sentiment[sentiment] = stats_sentiment.get(sentiment, 0) + 1
            
            # Compter par langue
            langue = doc.get('langue_nom', 'inconnu')
            stats_langue[langue] = stats_langue.get(langue, 0) + 1
            
            # Compter par plateforme
            plateforme = doc.get('plateforme', 'inconnu')
            stats_plateforme[plateforme] = stats_plateforme.get(plateforme, 0) + 1
        
        # Afficher les statistiques
        logger.info(f"\nRépartition par sentiment:")
        for sentiment, count in sorted(stats_sentiment.items(), key=lambda x: x[1], reverse=True):
            pourcentage = (count / total_docs * 100) if total_docs > 0 else 0
            logger.info(f"  {sentiment}: {count} documents ({pourcentage:.1f}%)")
        
        logger.info(f"\nRépartition par langue:")
        for langue, count in sorted(stats_langue.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {langue}: {count} documents")
        
        logger.info(f"\nRépartition par plateforme:")
        for plateforme, count in sorted(stats_plateforme.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {plateforme}: {count} documents")
    
    def afficher_exemple_requete_elasticsearch(self):
        """Affiche des exemples de requêtes Elasticsearch possibles"""
        logger.info(f"\n🔍 EXEMPLES DE REQUÊTES ELASTICSEARCH POSSIBLES")
        logger.info("-" * 60)
        
        exemples_requetes = [
            {
                "nom": "Recherche par sentiment négatif",
                "requete": {
                    "query": {
                        "term": {"sentiment": "négatif"}
                    }
                }
            },
            {
                "nom": "Recherche par langue anglaise",
                "requete": {
                    "query": {
                        "term": {"langue": "en"}
                    }
                }
            },
            {
                "nom": "Recherche textuelle dans le contenu",
                "requete": {
                    "query": {
                        "match": {"contenu": "bullying harassment"}
                    }
                }
            },
            {
                "nom": "Agrégation par plateforme",
                "requete": {
                    "size": 0,
                    "aggs": {
                        "plateformes": {
                            "terms": {"field": "plateforme"}
                        }
                    }
                }
            }
        ]
        
        for i, exemple in enumerate(exemples_requetes, 1):
            logger.info(f"\nExemple {i}: {exemple['nom']}")
            logger.info(f"Requête JSON:")
            logger.info(json.dumps(exemple['requete'], indent=2, ensure_ascii=False))
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("Connexions fermées")

def main():
    """Fonction principale"""
    indexeur = None
    
    try:
        # Initialiser l'indexeur en mode simulation
        indexeur = IndexeurElasticsearchSimulation()
        
        # Afficher le mapping
        indexeur.creer_mapping_index_simulation()
        
        # Simuler l'indexation
        documents_indexes = indexeur.simuler_indexation()
        
        if documents_indexes > 0:
            # Vérifier la simulation
            indexeur.verifier_indexation_simulation()
            
            # Afficher des exemples
            indexeur.afficher_exemples_simulation()
            
            # Afficher les statistiques
            indexeur.obtenir_statistiques_simulation()
            
            # Afficher des exemples de requêtes
            indexeur.afficher_exemple_requete_elasticsearch()
            
            logger.info("\n✅ ÉTAPE 4 SIMULÉE AVEC SUCCÈS !")
            logger.info("✅ Index 'harcelement_posts' simulé")
            logger.info("✅ Données converties de MongoDB vers format Elasticsearch")
            logger.info("✅ Champs requis présents: titre, contenu, auteur, date, URL, langue, sentiment, score")
            logger.info("✅ Mapping optimisé pour recherche et agrégations")
            logger.info("✅ Prêt pour indexation réelle avec Elasticsearch")
        else:
            logger.error("❌ Aucun document n'a été simulé")
        
    except KeyboardInterrupt:
        logger.info("Simulation interrompue par l'utilisateur")
    except Exception as e:
        logger.error(f"Erreur: {e}")
    finally:
        if indexeur:
            indexeur.fermer_connexions()

if __name__ == "__main__":
    main()
