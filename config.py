import os
from typing import List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for the social media scraper"""
    
    def __init__(self):
        # MongoDB Configuration
        self.MONGODB_URI = os.getenv("MONGODB_URI", "mongodb://localhost:27017/")
        self.MONGODB_DATABASE = os.getenv("MONGODB_DATABASE", "harcelement")
        self.MONGODB_COLLECTION = os.getenv("MONGODB_COLLECTION", "posts")
        
        # Reddit API Configuration
        self.REDDIT_CLIENT_ID = os.getenv("REDDIT_CLIENT_ID", "naoE5J20fwcnusZT_C0vkA")
        self.REDDIT_CLIENT_SECRET = os.getenv("REDDIT_CLIENT_SECRET", "2827ZUySYcMwUp0TfyA8JG5cTvI7cw")
        self.REDDIT_USER_AGENT = os.getenv("REDDIT_USER_AGENT", "harcelement script by /u/Fit-Juice-9025")
        self.REDDIT_SUBREDDITS = self._parse_list(os.getenv("REDDIT_SUBREDDITS", "bullying,TrueOffMyChest,cyberbullying,mentalhealth"))
        self.REDDIT_LIMIT_POSTS = int(os.getenv("REDDIT_LIMIT_POSTS", "100"))
        
        # Twitter API Configuration
        self.TWITTER_BEARER_TOKEN = os.getenv("TWITTER_BEARER_TOKEN", "")
        self.TWITTER_API_KEY = os.getenv("TWITTER_API_KEY", "")
        self.TWITTER_API_SECRET = os.getenv("TWITTER_API_SECRET", "")
        self.TWITTER_ACCESS_TOKEN = os.getenv("TWITTER_ACCESS_TOKEN", "")
        self.TWITTER_ACCESS_TOKEN_SECRET = os.getenv("TWITTER_ACCESS_TOKEN_SECRET", "")
        self.TWITTER_KEYWORDS = self._parse_list(os.getenv("TWITTER_KEYWORDS", "harassment,bullying,cyberbullying,online harassment"))
        self.TWITTER_LIMIT_TWEETS = int(os.getenv("TWITTER_LIMIT_TWEETS", "100"))
        
        # Telegram Configuration
        self.TELEGRAM_API_ID = os.getenv("TELEGRAM_API_ID", "")
        self.TELEGRAM_API_HASH = os.getenv("TELEGRAM_API_HASH", "")
        self.TELEGRAM_PHONE = os.getenv("TELEGRAM_PHONE", "")
        self.TELEGRAM_CHANNELS = self._parse_list(os.getenv("TELEGRAM_CHANNELS", ""))
        self.TELEGRAM_LIMIT_MESSAGES = int(os.getenv("TELEGRAM_LIMIT_MESSAGES", "100"))
        
        # General Configuration
        self.RATE_LIMIT_DELAY = float(os.getenv("RATE_LIMIT_DELAY", "2.0"))
        self.LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
        self.MAX_RETRIES = int(os.getenv("MAX_RETRIES", "3"))
        
        # Harassment-related keywords for filtering
        self.HARASSMENT_KEYWORDS = self._parse_list(os.getenv("HARASSMENT_KEYWORDS", 
            "harassment,bullying,cyberbullying,online harassment,trolling,abuse,intimidation,threats,stalking"))
    
    def _parse_list(self, value: str) -> List[str]:
        """Parse comma-separated string into list"""
        if not value:
            return []
        return [item.strip() for item in value.split(",") if item.strip()]
    
    def validate_config(self) -> bool:
        """Validate that required configuration is present"""
        errors = []
        
        # Check Reddit config
        if not self.REDDIT_CLIENT_ID or not self.REDDIT_CLIENT_SECRET:
            errors.append("Reddit API credentials are missing")
        
        # Check Twitter config (optional but warn if missing)
        if not self.TWITTER_BEARER_TOKEN and not self.TWITTER_API_KEY:
            print("Warning: Twitter API credentials are missing - Twitter scraping will be disabled")
        
        # Check Telegram config (optional but warn if missing)
        if not self.TELEGRAM_API_ID or not self.TELEGRAM_API_HASH:
            print("Warning: Telegram API credentials are missing - Telegram scraping will be disabled")
        
        if errors:
            for error in errors:
                print(f"Configuration Error: {error}")
            return False
        
        return True
    
    def get_mongodb_config(self) -> dict:
        """Get MongoDB configuration as dictionary"""
        return {
            "uri": self.MONGODB_URI,
            "database": self.MONGODB_DATABASE,
            "collection": self.MONGODB_COLLECTION
        }
    
    def get_reddit_config(self) -> dict:
        """Get Reddit configuration as dictionary"""
        return {
            "client_id": self.REDDIT_CLIENT_ID,
            "client_secret": self.REDDIT_CLIENT_SECRET,
            "user_agent": self.REDDIT_USER_AGENT,
            "subreddits": self.REDDIT_SUBREDDITS,
            "limit_posts": self.REDDIT_LIMIT_POSTS
        }
    
    def get_twitter_config(self) -> dict:
        """Get Twitter configuration as dictionary"""
        return {
            "bearer_token": self.TWITTER_BEARER_TOKEN,
            "api_key": self.TWITTER_API_KEY,
            "api_secret": self.TWITTER_API_SECRET,
            "access_token": self.TWITTER_ACCESS_TOKEN,
            "access_token_secret": self.TWITTER_ACCESS_TOKEN_SECRET,
            "keywords": self.TWITTER_KEYWORDS,
            "limit_tweets": self.TWITTER_LIMIT_TWEETS
        }
    
    def get_telegram_config(self) -> dict:
        """Get Telegram configuration as dictionary"""
        return {
            "api_id": self.TELEGRAM_API_ID,
            "api_hash": self.TELEGRAM_API_HASH,
            "phone": self.TELEGRAM_PHONE,
            "channels": self.TELEGRAM_CHANNELS,
            "limit_messages": self.TELEGRAM_LIMIT_MESSAGES
        }
