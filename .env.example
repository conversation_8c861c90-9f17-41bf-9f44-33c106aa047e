# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/
MONGODB_DATABASE=harcelement
MONGODB_COLLECTION=posts

# Reddit API Configuration
# Get these from: https://www.reddit.com/prefs/apps
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=harassment_scraper_v1.0_by_your_username
REDDIT_SUBREDDITS=bullying,TrueOffMyChest,cyberbullying,mentalhealth,depression,anxiety
REDDIT_LIMIT_POSTS=100

# Twitter API Configuration
# Get these from: https://developer.twitter.com/en/portal/dashboard
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret
TWITTER_KEYWORDS=harassment,bullying,cyberbullying,online harassment,trolling,abuse
TWITTER_LIMIT_TWEETS=100

# Telegram Configuration
# Get these from: https://my.telegram.org/apps
TELEGRAM_API_ID=your_telegram_api_id
TELEGRAM_API_HASH=your_telegram_api_hash
TELEGRAM_PHONE=your_phone_number_with_country_code
TELEGRAM_CHANNELS=channel1,channel2,channel3
TELEGRAM_LIMIT_MESSAGES=100

# General Configuration
RATE_LIMIT_DELAY=2.0
LOG_LEVEL=INFO
MAX_RETRIES=3

# Harassment Keywords for Filtering
HARASSMENT_KEYWORDS=harassment,bullying,cyberbullying,online harassment,trolling,abuse,intimidation,threats,stalking,hate speech
