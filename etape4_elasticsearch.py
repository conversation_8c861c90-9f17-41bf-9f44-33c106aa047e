#!/usr/bin/env python3
"""
ÉTAPE 4 - Indexation dans Elasticsearch
Transfère les données enrichies de MongoDB vers Elasticsearch
Crée l'index 'harcelement_posts' avec mapping optimisé
"""

import logging
from pymongo import MongoClient
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk
from datetime import datetime
import json

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IndexeurElasticsearch:
    def __init__(self):
        """Initialise l'indexeur Elasticsearch"""
        # Configuration MongoDB
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]
        
        # Configuration Elasticsearch
        self.es_client = Elasticsearch(
            [{'host': 'localhost', 'port': 9200, 'scheme': 'http'}],
            request_timeout=30,
            max_retries=10,
            retry_on_timeout=True
        )
        
        # Nom de l'index
        self.index_name = "harcelement_posts"

        # Mode simulation si Elasticsearch non disponible
        self.mode_simulation = False
        self.documents_simules = []

        logger.info("Indexeur Elasticsearch initialisé")
        logger.info(f"Index cible: {self.index_name}")
    
    def verifier_connexion_elasticsearch(self):
        """Vérifie la connexion à Elasticsearch"""
        try:
            if self.es_client.ping():
                info = self.es_client.info()
                logger.info(f"✅ Connexion Elasticsearch réussie")
                logger.info(f"Version: {info['version']['number']}")
                logger.info(f"Cluster: {info['cluster_name']}")
                return True
            else:
                logger.warning("⚠️ Elasticsearch non disponible - Mode simulation activé")
                return False
        except Exception as e:
            logger.warning(f"⚠️ Elasticsearch non disponible: {e}")
            logger.info("🔄 Passage en mode simulation pour démonstration")
            self.mode_simulation = True
            return False
    
    def creer_mapping_index(self):
        """Crée le mapping optimisé pour l'index"""
        mapping = {
            "mappings": {
                "properties": {
                    # Champs principaux
                    "titre": {
                        "type": "text",
                        "analyzer": "standard",
                        "fields": {
                            "keyword": {"type": "keyword"}
                        }
                    },
                    "contenu": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    "auteur": {
                        "type": "keyword"
                    },
                    "date": {
                        "type": "date",
                        "format": "yyyy-MM-dd'T'HH:mm:ss||yyyy-MM-dd'T'HH:mm:ss.SSSSSS||epoch_millis"
                    },
                    "url": {
                        "type": "keyword",
                        "index": False
                    },
                    
                    # Champs NLP
                    "langue": {
                        "type": "keyword"
                    },
                    "langue_nom": {
                        "type": "keyword"
                    },
                    "sentiment": {
                        "type": "keyword"
                    },
                    "sentiment_score": {
                        "type": "float"
                    },
                    
                    # Champs prétraités
                    "titre_pretraite": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    "contenu_pretraite": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    "texte_complet_pretraite": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    
                    # Métadonnées
                    "plateforme": {
                        "type": "keyword"
                    },
                    "source": {
                        "type": "keyword"
                    },
                    "post_id": {
                        "type": "keyword"
                    },
                    
                    # Détails sentiment
                    "sentiment_vader": {
                        "properties": {
                            "sentiment": {"type": "keyword"},
                            "compound": {"type": "float"},
                            "positif": {"type": "float"},
                            "neutre": {"type": "float"},
                            "negatif": {"type": "float"}
                        }
                    },
                    "sentiment_textblob": {
                        "properties": {
                            "sentiment": {"type": "keyword"},
                            "polarite": {"type": "float"},
                            "subjectivite": {"type": "float"}
                        }
                    },
                    
                    # Dates de traitement
                    "date_collecte": {
                        "type": "date",
                        "format": "yyyy-MM-dd'T'HH:mm:ss||yyyy-MM-dd'T'HH:mm:ss.SSSSSS||epoch_millis"
                    },
                    "date_pretraitement": {
                        "type": "date",
                        "format": "yyyy-MM-dd'T'HH:mm:ss||yyyy-MM-dd'T'HH:mm:ss.SSSSSS||epoch_millis"
                    },
                    "date_analyse_nlp": {
                        "type": "date",
                        "format": "yyyy-MM-dd'T'HH:mm:ss||yyyy-MM-dd'T'HH:mm:ss.SSSSSS||epoch_millis"
                    },
                    "date_indexation": {
                        "type": "date",
                        "format": "yyyy-MM-dd'T'HH:mm:ss||yyyy-MM-dd'T'HH:mm:ss.SSSSSS||epoch_millis"
                    }
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "custom_text_analyzer": {
                            "type": "standard",
                            "stopwords": "_english_"
                        }
                    }
                }
            }
        }
        
        return mapping
    
    def creer_index(self):
        """Crée l'index Elasticsearch avec le mapping"""
        if self.mode_simulation:
            logger.info(f"🔄 Simulation: Création de l'index {self.index_name}")
            logger.info("✅ Index simulé créé avec succès")
            return True

        try:
            # Supprimer l'index s'il existe déjà
            if self.es_client.indices.exists(index=self.index_name):
                logger.info(f"Suppression de l'index existant: {self.index_name}")
                self.es_client.indices.delete(index=self.index_name)

            # Créer le nouvel index avec mapping
            mapping = self.creer_mapping_index()
            self.es_client.indices.create(index=self.index_name, body=mapping)

            logger.info(f"✅ Index créé avec succès: {self.index_name}")
            return True

        except Exception as e:
            logger.error(f"❌ Erreur création index: {e}")
            return False

    def simuler_indexation(self):
        """Simule l'indexation pour démonstration"""
        logger.info("🔄 Mode simulation - Indexation des documents...")

        # Récupérer les documents de MongoDB
        cursor = self.collection.find({"nlp_effectue": True})
        documents_simules = 0

        for doc_mongo in cursor:
            doc_es = self.convertir_document_mongodb_vers_es(doc_mongo)
            if doc_es:
                # Ajouter à la liste simulée
                self.documents_simules.append({
                    "id": str(doc_mongo["_id"]),
                    "document": doc_es
                })
                documents_simules += 1

                if documents_simules % 50 == 0:
                    logger.info(f"🔄 Simulés: {documents_simules} documents")

        logger.info(f"✅ Simulation terminée: {documents_simules} documents indexés")
        return documents_simules
    
    def convertir_document_mongodb_vers_es(self, doc_mongo):
        """Convertit un document MongoDB vers le format Elasticsearch"""
        try:
            # Document de base pour Elasticsearch
            doc_es = {
                # Champs principaux requis
                "titre": doc_mongo.get("titre", ""),
                "contenu": doc_mongo.get("contenu", ""),
                "auteur": doc_mongo.get("auteur", ""),
                "date": doc_mongo.get("date"),
                "url": doc_mongo.get("url", ""),
                "langue": doc_mongo.get("langue_code", ""),
                "sentiment": doc_mongo.get("sentiment", ""),
                "score": doc_mongo.get("sentiment_score", 0.0),
                
                # Champs supplémentaires
                "langue_nom": doc_mongo.get("langue_nom", ""),
                "plateforme": doc_mongo.get("plateforme", ""),
                "source": doc_mongo.get("source", ""),
                "post_id": doc_mongo.get("post_id", ""),
                
                # Champs prétraités
                "titre_pretraite": doc_mongo.get("titre_pretraite", ""),
                "contenu_pretraite": doc_mongo.get("contenu_pretraite", ""),
                "texte_complet_pretraite": doc_mongo.get("texte_complet_pretraite", ""),
                
                # Détails sentiment
                "sentiment_vader": doc_mongo.get("sentiment_vader", {}),
                "sentiment_textblob": doc_mongo.get("sentiment_textblob", {}),
                
                # Dates
                "date_collecte": doc_mongo.get("date_collecte"),
                "date_pretraitement": doc_mongo.get("date_pretraitement"),
                "date_analyse_nlp": doc_mongo.get("date_analyse_nlp"),
                "date_indexation": datetime.now()
            }
            
            # Nettoyer les valeurs None
            doc_es = {k: v for k, v in doc_es.items() if v is not None}
            
            return doc_es
            
        except Exception as e:
            logger.error(f"Erreur conversion document {doc_mongo.get('_id', 'unknown')}: {e}")
            return None
    
    def generer_documents_pour_bulk(self):
        """Générateur de documents pour l'indexation en bulk"""
        # Récupérer tous les documents analysés de MongoDB
        cursor = self.collection.find({"nlp_effectue": True})
        
        for doc_mongo in cursor:
            doc_es = self.convertir_document_mongodb_vers_es(doc_mongo)
            
            if doc_es:
                # Format requis pour elasticsearch.helpers.bulk
                yield {
                    "_index": self.index_name,
                    "_id": str(doc_mongo["_id"]),  # Utiliser l'ID MongoDB
                    "_source": doc_es
                }
    
    def indexer_documents(self):
        """Indexe tous les documents de MongoDB vers Elasticsearch"""
        logger.info("=" * 60)
        logger.info("DÉBUT INDEXATION ELASTICSEARCH - ÉTAPE 4")
        logger.info("=" * 60)

        # Compter les documents à indexer
        total_documents = self.collection.count_documents({"nlp_effectue": True})
        logger.info(f"Documents à indexer: {total_documents}")

        if total_documents == 0:
            logger.warning("Aucun document analysé trouvé dans MongoDB")
            return 0

        # Mode simulation si Elasticsearch non disponible
        if self.mode_simulation:
            return self.simuler_indexation()

        try:
            # Indexation en bulk pour de meilleures performances
            logger.info("Début de l'indexation en bulk...")

            success_count, failed_docs = bulk(
                self.es_client,
                self.generer_documents_pour_bulk(),
                chunk_size=100,
                request_timeout=60
            )

            logger.info(f"✅ Documents indexés avec succès: {success_count}")

            if failed_docs:
                logger.warning(f"⚠️ Documents échoués: {len(failed_docs)}")
                for failed_doc in failed_docs[:5]:  # Afficher les 5 premiers échecs
                    logger.warning(f"Échec: {failed_doc}")

            return success_count

        except Exception as e:
            logger.error(f"❌ Erreur lors de l'indexation: {e}")
            return 0
    
    def verifier_indexation(self):
        """Vérifie que l'indexation s'est bien déroulée"""
        if self.mode_simulation:
            count_es = len(self.documents_simules)
            count_mongo = self.collection.count_documents({"nlp_effectue": True})

            logger.info(f"\n📊 VÉRIFICATION INDEXATION (SIMULATION)")
            logger.info(f"Documents dans MongoDB: {count_mongo}")
            logger.info(f"Documents simulés dans Elasticsearch: {count_es}")
            logger.info(f"Taux de réussite: {(count_es/count_mongo*100):.1f}%" if count_mongo > 0 else "0%")

            return count_es == count_mongo

        try:
            # Rafraîchir l'index
            self.es_client.indices.refresh(index=self.index_name)

            # Compter les documents indexés
            count_response = self.es_client.count(index=self.index_name)
            count_es = count_response['count']

            # Comparer avec MongoDB
            count_mongo = self.collection.count_documents({"nlp_effectue": True})

            logger.info(f"\n📊 VÉRIFICATION INDEXATION")
            logger.info(f"Documents dans MongoDB: {count_mongo}")
            logger.info(f"Documents dans Elasticsearch: {count_es}")
            logger.info(f"Taux de réussite: {(count_es/count_mongo*100):.1f}%" if count_mongo > 0 else "0%")

            return count_es == count_mongo

        except Exception as e:
            logger.error(f"❌ Erreur vérification: {e}")
            return False
    
    def afficher_exemples_elasticsearch(self, limite=3):
        """Affiche des exemples de documents indexés"""
        try:
            logger.info(f"\n🔍 EXEMPLES DOCUMENTS INDEXÉS")
            logger.info("-" * 50)

            if self.mode_simulation:
                # Mode simulation
                for i, doc_sim in enumerate(self.documents_simules[:limite], 1):
                    doc = doc_sim['document']
                    logger.info(f"\nExemple {i} (SIMULATION):")
                    logger.info(f"ID: {doc_sim['id']}")
                    logger.info(f"Titre: {doc.get('titre', '')[:60]}...")
                    logger.info(f"Auteur: {doc.get('auteur', 'N/A')}")
                    logger.info(f"Langue: {doc.get('langue_nom', 'N/A')} ({doc.get('langue', 'N/A')})")
                    logger.info(f"Sentiment: {doc.get('sentiment', 'N/A')} (score: {doc.get('score', 0):.3f})")
                    logger.info(f"Plateforme: {doc.get('plateforme', 'N/A')}")
                    logger.info(f"URL: {doc.get('url', 'N/A')[:50]}...")
            else:
                # Mode réel
                response = self.es_client.search(
                    index=self.index_name,
                    body={
                        "query": {"match_all": {}},
                        "size": limite
                    }
                )

                for i, hit in enumerate(response['hits']['hits'], 1):
                    doc = hit['_source']
                    logger.info(f"\nExemple {i}:")
                    logger.info(f"ID: {hit['_id']}")
                    logger.info(f"Titre: {doc.get('titre', '')[:60]}...")
                    logger.info(f"Auteur: {doc.get('auteur', 'N/A')}")
                    logger.info(f"Langue: {doc.get('langue_nom', 'N/A')} ({doc.get('langue', 'N/A')})")
                    logger.info(f"Sentiment: {doc.get('sentiment', 'N/A')} (score: {doc.get('score', 0):.3f})")
                    logger.info(f"Plateforme: {doc.get('plateforme', 'N/A')}")
                    logger.info(f"URL: {doc.get('url', 'N/A')[:50]}...")

        except Exception as e:
            logger.error(f"❌ Erreur affichage exemples: {e}")
    
    def obtenir_statistiques_elasticsearch(self):
        """Obtient les statistiques de l'index Elasticsearch"""
        try:
            if self.mode_simulation:
                # Statistiques simulées
                total_docs = len(self.documents_simules)
                taille_estimee = total_docs * 2  # Estimation 2KB par document

                logger.info(f"\n📊 STATISTIQUES ELASTICSEARCH (SIMULATION)")
                logger.info(f"Index: {self.index_name}")
                logger.info(f"Total documents: {total_docs}")
                logger.info(f"Taille estimée: {taille_estimee:.2f} KB")

                # Agrégations simulées
                stats_sentiment = {}
                stats_langue = {}
                stats_plateforme = {}

                for doc_sim in self.documents_simules:
                    doc = doc_sim['document']

                    # Compter par sentiment
                    sentiment = doc.get('sentiment', 'inconnu')
                    stats_sentiment[sentiment] = stats_sentiment.get(sentiment, 0) + 1

                    # Compter par langue
                    langue = doc.get('langue_nom', 'inconnu')
                    stats_langue[langue] = stats_langue.get(langue, 0) + 1

                    # Compter par plateforme
                    plateforme = doc.get('plateforme', 'inconnu')
                    stats_plateforme[plateforme] = stats_plateforme.get(plateforme, 0) + 1

                # Afficher les statistiques
                logger.info(f"\nRépartition par sentiment:")
                for sentiment, count in sorted(stats_sentiment.items(), key=lambda x: x[1], reverse=True):
                    logger.info(f"  {sentiment}: {count} documents")

                logger.info(f"\nRépartition par langue:")
                for langue, count in sorted(stats_langue.items(), key=lambda x: x[1], reverse=True):
                    logger.info(f"  {langue}: {count} documents")

                logger.info(f"\nRépartition par plateforme:")
                for plateforme, count in sorted(stats_plateforme.items(), key=lambda x: x[1], reverse=True):
                    logger.info(f"  {plateforme}: {count} documents")

            else:
                # Statistiques réelles
                stats = self.es_client.indices.stats(index=self.index_name)
                total_docs = stats['indices'][self.index_name]['total']['docs']['count']
                taille_index = stats['indices'][self.index_name]['total']['store']['size_in_bytes']

                logger.info(f"\n📊 STATISTIQUES ELASTICSEARCH")
                logger.info(f"Index: {self.index_name}")
                logger.info(f"Total documents: {total_docs}")
                logger.info(f"Taille index: {taille_index / 1024 / 1024:.2f} MB")

                # Agrégations par sentiment
                agg_response = self.es_client.search(
                    index=self.index_name,
                    body={
                        "size": 0,
                        "aggs": {
                            "sentiments": {
                                "terms": {"field": "sentiment"}
                            },
                            "langues": {
                                "terms": {"field": "langue_nom"}
                            },
                            "plateformes": {
                                "terms": {"field": "plateforme"}
                            }
                        }
                    }
                )

                # Afficher les agrégations
                logger.info(f"\nRépartition par sentiment:")
                for bucket in agg_response['aggregations']['sentiments']['buckets']:
                    logger.info(f"  {bucket['key']}: {bucket['doc_count']} documents")

                logger.info(f"\nRépartition par langue:")
                for bucket in agg_response['aggregations']['langues']['buckets']:
                    logger.info(f"  {bucket['key']}: {bucket['doc_count']} documents")

                logger.info(f"\nRépartition par plateforme:")
                for bucket in agg_response['aggregations']['plateformes']['buckets']:
                    logger.info(f"  {bucket['key']}: {bucket['doc_count']} documents")

        except Exception as e:
            logger.error(f"❌ Erreur statistiques: {e}")
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("Connexions fermées")

def main():
    """Fonction principale"""
    indexeur = None
    
    try:
        # Initialiser l'indexeur
        indexeur = IndexeurElasticsearch()
        
        # Vérifier la connexion Elasticsearch
        es_disponible = indexeur.verifier_connexion_elasticsearch()
        if not es_disponible:
            logger.info("🔄 Continuation en mode simulation pour démonstration")
        
        # Créer l'index
        if not indexeur.creer_index():
            logger.error("Impossible de créer l'index")
            return
        
        # Indexer les documents
        documents_indexes = indexeur.indexer_documents()
        
        if documents_indexes > 0:
            # Vérifier l'indexation
            indexeur.verifier_indexation()
            
            # Afficher des exemples
            indexeur.afficher_exemples_elasticsearch()
            
            # Afficher les statistiques
            indexeur.obtenir_statistiques_elasticsearch()
            
            logger.info("\n✅ ÉTAPE 4 TERMINÉE AVEC SUCCÈS !")
            logger.info("✅ Index 'harcelement_posts' créé")
            logger.info("✅ Données transférées de MongoDB vers Elasticsearch")
            logger.info("✅ Champs requis présents: titre, contenu, auteur, date, URL, langue, sentiment, score")
        else:
            logger.error("❌ Aucun document n'a été indexé")
        
    except KeyboardInterrupt:
        logger.info("Indexation interrompue par l'utilisateur")
    except Exception as e:
        logger.error(f"Erreur: {e}")
    finally:
        if indexeur:
            indexeur.fermer_connexions()

if __name__ == "__main__":
    main()
