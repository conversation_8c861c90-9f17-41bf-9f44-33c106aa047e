from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.errors import DuplicateKeyError, ConnectionFailure
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from config import Config

logger = logging.getLogger(__name__)

class DatabaseManager:
    """MongoDB database manager for social media posts"""
    
    def __init__(self, config: Config):
        self.config = config
        self.client = None
        self.db = None
        self.collection = None
        self.connect()
        self.setup_indexes()
    
    def connect(self):
        """Connect to MongoDB"""
        try:
            self.client = MongoClient(self.config.MONGODB_URI)
            self.db = self.client[self.config.MONGODB_DATABASE]
            self.collection = self.db[self.config.MONGODB_COLLECTION]
            
            # Test connection
            self.client.admin.command('ping')
            logger.info(f"Connected to MongoDB: {self.config.MONGODB_DATABASE}.{self.config.MONGODB_COLLECTION}")
            
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
    
    def setup_indexes(self):
        """Create indexes for better performance"""
        try:
            # Create unique index on URL to prevent duplicates
            self.collection.create_index("url", unique=True)
            
            # Create indexes for common queries
            self.collection.create_index("platform")
            self.collection.create_index("source")
            self.collection.create_index("date")
            self.collection.create_index("author")
            
            # Create text index for content search
            self.collection.create_index([
                ("title", "text"),
                ("content", "text")
            ])
            
            # Create compound index for platform and date
            self.collection.create_index([
                ("platform", ASCENDING),
                ("date", DESCENDING)
            ])
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.warning(f"Error creating indexes: {e}")
    
    def insert_post(self, post_data: Dict) -> bool:
        """Insert a post into the database"""
        try:
            # Add metadata
            post_data["created_at"] = datetime.utcnow()
            post_data["updated_at"] = datetime.utcnow()
            
            # Validate required fields
            if not self._validate_post_data(post_data):
                return False
            
            self.collection.insert_one(post_data)
            logger.debug(f"Inserted post: {post_data.get('title', 'No title')[:50]}...")
            return True
            
        except DuplicateKeyError:
            logger.debug(f"Post already exists: {post_data.get('url', 'No URL')}")
            return False
        except Exception as e:
            logger.error(f"Error inserting post: {e}")
            return False
    
    def _validate_post_data(self, post_data: Dict) -> bool:
        """Validate post data before insertion"""
        required_fields = ["title", "content", "author", "date", "url", "source", "platform"]
        
        for field in required_fields:
            if field not in post_data:
                logger.warning(f"Missing required field: {field}")
                return False
        
        # Validate URL
        if not post_data["url"] or not isinstance(post_data["url"], str):
            logger.warning("Invalid URL")
            return False
        
        # Validate date
        if not isinstance(post_data["date"], datetime):
            logger.warning("Invalid date format")
            return False
        
        return True
    
    def get_posts_count(self, platform: Optional[str] = None) -> int:
        """Get total number of posts"""
        try:
            if platform:
                return self.collection.count_documents({"platform": platform})
            return self.collection.count_documents({})
        except Exception as e:
            logger.error(f"Error counting posts: {e}")
            return 0
    
    def get_posts_by_platform(self, platform: str, limit: int = 100) -> List[Dict]:
        """Get posts by platform"""
        try:
            cursor = self.collection.find({"platform": platform}).limit(limit)
            return list(cursor)
        except Exception as e:
            logger.error(f"Error retrieving posts: {e}")
            return []
    
    def get_recent_posts(self, days: int = 7, limit: int = 100) -> List[Dict]:
        """Get recent posts from the last N days"""
        try:
            from datetime import timedelta
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            cursor = self.collection.find({
                "date": {"$gte": cutoff_date}
            }).sort("date", DESCENDING).limit(limit)
            
            return list(cursor)
        except Exception as e:
            logger.error(f"Error retrieving recent posts: {e}")
            return []
    
    def search_posts(self, query: str, limit: int = 100) -> List[Dict]:
        """Search posts by text content"""
        try:
            cursor = self.collection.find({
                "$text": {"$search": query}
            }).limit(limit)
            
            return list(cursor)
        except Exception as e:
            logger.error(f"Error searching posts: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            stats = {
                "total_posts": self.collection.count_documents({}),
                "platforms": {},
                "sources": {},
                "date_range": {}
            }
            
            # Count by platform
            platform_pipeline = [
                {"$group": {"_id": "$platform", "count": {"$sum": 1}}}
            ]
            for result in self.collection.aggregate(platform_pipeline):
                stats["platforms"][result["_id"]] = result["count"]
            
            # Count by source
            source_pipeline = [
                {"$group": {"_id": "$source", "count": {"$sum": 1}}}
            ]
            for result in self.collection.aggregate(source_pipeline):
                stats["sources"][result["_id"]] = result["count"]
            
            # Get date range
            date_pipeline = [
                {"$group": {
                    "_id": None,
                    "min_date": {"$min": "$date"},
                    "max_date": {"$max": "$date"}
                }}
            ]
            date_result = list(self.collection.aggregate(date_pipeline))
            if date_result:
                stats["date_range"] = {
                    "earliest": date_result[0]["min_date"],
                    "latest": date_result[0]["max_date"]
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {}
    
    def close_connection(self):
        """Close database connection"""
        if self.client:
            self.client.close()
            logger.info("Database connection closed")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close_connection()
