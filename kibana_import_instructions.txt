
# INSTRUCTIONS D'IMPORT DANS KIBANA

## 1. Prérequis
- Elasticsearch et Kibana démarrés
- Index 'harcelement_posts' créé dans Elasticsearch
- Données indexées depuis l'Étape 4

## 2. Import de la configuration
1. <PERSON><PERSON><PERSON><PERSON><PERSON> (http://localhost:5601)
2. <PERSON><PERSON> dans "Stack Management" > "Saved Objects"
3. <PERSON><PERSON><PERSON> sur "Import"
4. Sélectionner le fichier 'kibana_dashboard_config.json'
5. Cliquer sur "Import"

## 3. Accès au dashboard
1. <PERSON><PERSON> dans "Analytics" > "Dashboard"
2. Ouvrir "Analyse du Harcèlement en Ligne"
3. Configurer la période temporelle si nécessaire

## 4. Filtres interactifs disponibles
- Langue (langue_nom)
- Sentiment (sentiment)
- Score de sentiment (score)
- Date (date)
- Plateforme (plateforme)
- Source (source)

## 5. Visualisations incluses
- Répartition des langues (camembert)
- Répartition des sentiments (histogramme coloré)
- Répartition par plateforme (camembert)
- Évolution temporelle (graphique linéaire)
- Contenus les plus négatifs (tableau)

## 6. Personnalisation
- Modifier les couleurs dans les options de visualisation
- Ajuster les périodes temporelles
- Ajouter des filtres personnalisés
- Créer de nouvelles visualisations
        