{"version": "8.0.0", "objects": [{"id": "harcelement_posts", "type": "index-pattern", "attributes": {"title": "harcelement_posts", "timeFieldName": "date", "fields": "[{\"name\": \"titre\", \"type\": \"string\", \"searchable\": true, \"aggregatable\": false}, {\"name\": \"contenu\", \"type\": \"string\", \"searchable\": true, \"aggregatable\": false}, {\"name\": \"auteur\", \"type\": \"string\", \"searchable\": true, \"aggregatable\": true}, {\"name\": \"date\", \"type\": \"date\", \"searchable\": true, \"aggregatable\": true}, {\"name\": \"url\", \"type\": \"string\", \"searchable\": false, \"aggregatable\": true}, {\"name\": \"langue\", \"type\": \"string\", \"searchable\": true, \"aggregatable\": true}, {\"name\": \"langue_nom\", \"type\": \"string\", \"searchable\": true, \"aggregatable\": true}, {\"name\": \"sentiment\", \"type\": \"string\", \"searchable\": true, \"aggregatable\": true}, {\"name\": \"score\", \"type\": \"number\", \"searchable\": true, \"aggregatable\": true}, {\"name\": \"plateforme\", \"type\": \"string\", \"searchable\": true, \"aggregatable\": true}, {\"name\": \"source\", \"type\": \"string\", \"searchable\": true, \"aggregatable\": true}]"}}, {"id": "langues-repartition", "type": "visualization", "attributes": {"title": "Répartition des Langues", "visState": "{\"title\": \"R\\u00e9partition des Langues\", \"type\": \"pie\", \"aggs\": [{\"id\": \"1\", \"type\": \"count\", \"schema\": \"metric\", \"params\": {}}, {\"id\": \"2\", \"type\": \"terms\", \"schema\": \"segment\", \"params\": {\"field\": \"langue_nom\", \"size\": 10, \"order\": \"desc\", \"orderBy\": \"1\"}}]}", "uiStateJSON": "{}", "description": "Répartition des documents par langue détectée", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\": \"harcelement_posts\", \"query\": {\"match_all\": {}}, \"filter\": []}"}}}, {"id": "sentiments-repartition", "type": "visualization", "attributes": {"title": "Répartition des Sentiments", "visState": "{\"title\": \"R\\u00e9partition des Sentiments\", \"type\": \"histogram\", \"aggs\": [{\"id\": \"1\", \"type\": \"count\", \"schema\": \"metric\", \"params\": {}}, {\"id\": \"2\", \"type\": \"terms\", \"schema\": \"segment\", \"params\": {\"field\": \"sentiment\", \"size\": 5, \"order\": \"desc\", \"orderBy\": \"1\"}}]}", "uiStateJSON": "{\"vis\": {\"colors\": {\"n\\u00e9gatif\": \"#E74C3C\", \"neutre\": \"#F39C12\", \"positif\": \"#27AE60\"}}}", "description": "Répartition des documents par sentiment (positif, neutre, négatif)", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\": \"harcelement_posts\", \"query\": {\"match_all\": {}}, \"filter\": []}"}}}, {"id": "evolution-temporell<PERSON>", "type": "visualization", "attributes": {"title": "Évolution Temporelle des Publications", "visState": "{\"title\": \"\\u00c9volution Temporelle des Publications\", \"type\": \"line\", \"aggs\": [{\"id\": \"1\", \"type\": \"count\", \"schema\": \"metric\", \"params\": {}}, {\"id\": \"2\", \"type\": \"date_histogram\", \"schema\": \"segment\", \"params\": {\"field\": \"date\", \"interval\": \"auto\", \"min_doc_count\": 1}}, {\"id\": \"3\", \"type\": \"terms\", \"schema\": \"group\", \"params\": {\"field\": \"sentiment\", \"size\": 3, \"order\": \"desc\", \"orderBy\": \"1\"}}]}", "uiStateJSON": "{}", "description": "Évolution du nombre de publications dans le temps, segmentée par sentiment", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\": \"harcelement_posts\", \"query\": {\"match_all\": {}}, \"filter\": []}"}}}, {"id": "contenus-negatifs", "type": "search", "attributes": {"title": "Contenus les Plus Négatifs", "description": "Liste des contenus avec les scores de sentiment les plus négatifs", "hits": 0, "columns": ["titre", "auteur", "score", "plateforme", "langue_nom"], "sort": ["score", "desc"], "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\": \"harcelement_posts\", \"query\": {\"bool\": {\"must\": [{\"term\": {\"sentiment\": \"n\\u00e9gatif\"}}, {\"range\": {\"score\": {\"lt\": -0.5}}}]}}, \"filter\": [], \"sort\": [{\"score\": {\"order\": \"asc\"}}]}"}}}, {"id": "repartition-plateformes", "type": "visualization", "attributes": {"title": "Répartition par Plateforme", "visState": "{\"title\": \"R\\u00e9partition par Plateforme\", \"type\": \"pie\", \"aggs\": [{\"id\": \"1\", \"type\": \"count\", \"schema\": \"metric\", \"params\": {}}, {\"id\": \"2\", \"type\": \"terms\", \"schema\": \"segment\", \"params\": {\"field\": \"plateforme\", \"size\": 10, \"order\": \"desc\", \"orderBy\": \"1\"}}]}", "uiStateJSON": "{\"vis\": {\"colors\": {\"reddit\": \"#FF4500\", \"twitter\": \"#1DA1F2\", \"telegram\": \"#0088CC\"}}}", "description": "Répartition des documents par plateforme source", "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"index\": \"harcelement_posts\", \"query\": {\"match_all\": {}}, \"filter\": []}"}}}, {"id": "harcelement-analysis-dashboard", "type": "dashboard", "attributes": {"title": "Ana<PERSON><PERSON> du Harcèlement en Ligne", "description": "Tableau de bord complet pour l'analyse des données de harcèlement collectées sur les réseaux sociaux", "panelsJSON": "[{\"id\": \"langues-repartition\", \"type\": \"visualization\", \"gridData\": {\"x\": 0, \"y\": 0, \"w\": 24, \"h\": 15}}, {\"id\": \"sentiments-repartition\", \"type\": \"visualization\", \"gridData\": {\"x\": 24, \"y\": 0, \"w\": 24, \"h\": 15}}, {\"id\": \"repartition-plateformes\", \"type\": \"visualization\", \"gridData\": {\"x\": 0, \"y\": 15, \"w\": 24, \"h\": 15}}, {\"id\": \"evolution-temporelle\", \"type\": \"visualization\", \"gridData\": {\"x\": 0, \"y\": 30, \"w\": 48, \"h\": 20}}, {\"id\": \"contenus-negatifs\", \"type\": \"search\", \"gridData\": {\"x\": 0, \"y\": 50, \"w\": 48, \"h\": 25}}]", "optionsJSON": "{\"useMargins\": true, \"syncColors\": false, \"hidePanelTitles\": false}", "timeRestore": true, "timeTo": "now", "timeFrom": "now-30d", "refreshInterval": {"pause": false, "value": 300000}, "kibanaSavedObjectMeta": {"searchSourceJSON": "{\"query\": {\"match_all\": {}}, \"filter\": []}"}}}]}