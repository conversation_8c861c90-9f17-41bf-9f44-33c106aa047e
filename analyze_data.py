#!/usr/bin/env python3
"""
Data analysis script for harassment data collection
Shows statistics and insights from collected data
"""

import logging
from datetime import datetime, timedelta
from collections import Counter
import json
from config import Config
from database import DatabaseManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class HarassmentDataAnalyzer:
    """Analyzer for harassment data"""
    
    def __init__(self):
        self.config = Config()
        self.db_manager = DatabaseManager(self.config)
    
    def get_basic_statistics(self):
        """Get basic statistics about the collected data"""
        logger.info("📊 HARASSMENT DATA ANALYSIS")
        logger.info("=" * 50)
        
        stats = self.db_manager.get_statistics()
        
        logger.info(f"Total posts in database: {stats.get('total_posts', 0)}")
        logger.info("\nPlatform distribution:")
        for platform, count in stats.get('platforms', {}).items():
            if platform:  # Skip None values
                logger.info(f"  {platform}: {count} posts")
        
        logger.info("\nSource distribution:")
        for source, count in stats.get('sources', {}).items():
            logger.info(f"  {source}: {count} posts")
        
        date_range = stats.get('date_range', {})
        if date_range:
            logger.info(f"\nDate range:")
            logger.info(f"  Earliest post: {date_range.get('earliest')}")
            logger.info(f"  Latest post: {date_range.get('latest')}")
    
    def analyze_recent_activity(self, days=7):
        """Analyze recent activity"""
        logger.info(f"\n📈 RECENT ACTIVITY (Last {days} days)")
        logger.info("-" * 30)
        
        recent_posts = self.db_manager.get_recent_posts(days=days, limit=1000)
        
        if not recent_posts:
            logger.info("No recent posts found")
            return
        
        logger.info(f"Recent posts: {len(recent_posts)}")
        
        # Group by platform
        platform_counts = Counter(post.get('platform') for post in recent_posts)
        logger.info("\nRecent posts by platform:")
        for platform, count in platform_counts.items():
            if platform:
                logger.info(f"  {platform}: {count}")
        
        # Group by source
        source_counts = Counter(post.get('source') for post in recent_posts)
        logger.info("\nRecent posts by source:")
        for source, count in source_counts.most_common(10):
            logger.info(f"  {source}: {count}")
    
    def analyze_content_keywords(self, limit=100):
        """Analyze content for harassment-related keywords"""
        logger.info(f"\n🔍 KEYWORD ANALYSIS")
        logger.info("-" * 20)
        
        # Get recent posts
        posts = self.db_manager.get_recent_posts(days=30, limit=limit)
        
        if not posts:
            logger.info("No posts found for analysis")
            return
        
        # Define harassment-related keywords
        harassment_keywords = [
            'bullying', 'harassment', 'cyberbullying', 'abuse', 'threats',
            'stalking', 'intimidation', 'trolling', 'hate', 'suicide',
            'depression', 'anxiety', 'mental health', 'help', 'scared'
        ]
        
        keyword_counts = Counter()
        total_analyzed = 0
        
        for post in posts:
            content = (post.get('title', '') + ' ' + post.get('content', '')).lower()
            total_analyzed += 1
            
            for keyword in harassment_keywords:
                if keyword in content:
                    keyword_counts[keyword] += 1
        
        logger.info(f"Analyzed {total_analyzed} posts")
        logger.info("\nTop harassment-related keywords:")
        for keyword, count in keyword_counts.most_common(10):
            percentage = (count / total_analyzed) * 100
            logger.info(f"  {keyword}: {count} posts ({percentage:.1f}%)")
    
    def show_sample_posts(self, platform=None, limit=5):
        """Show sample posts"""
        logger.info(f"\n📝 SAMPLE POSTS")
        if platform:
            logger.info(f"Platform: {platform}")
        logger.info("-" * 20)
        
        if platform:
            posts = self.db_manager.get_posts_by_platform(platform, limit)
        else:
            posts = self.db_manager.get_recent_posts(days=7, limit=limit)
        
        for i, post in enumerate(posts, 1):
            logger.info(f"\n{i}. {post.get('title', 'No title')}")
            logger.info(f"   Source: {post.get('source', 'Unknown')}")
            logger.info(f"   Author: {post.get('author', 'Unknown')}")
            logger.info(f"   Date: {post.get('date', 'Unknown')}")
            logger.info(f"   URL: {post.get('url', 'No URL')}")
            
            content = post.get('content', '')
            if content:
                preview = content[:200] + "..." if len(content) > 200 else content
                logger.info(f"   Content: {preview}")
    
    def export_summary_report(self, filename=None):
        """Export a summary report"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"harassment_analysis_report_{timestamp}.json"
        
        logger.info(f"\n💾 EXPORTING REPORT")
        logger.info("-" * 20)
        
        # Gather all data
        stats = self.db_manager.get_statistics()
        recent_posts = self.db_manager.get_recent_posts(days=7, limit=100)
        
        # Create report
        report = {
            "generated_at": datetime.now().isoformat(),
            "database_statistics": stats,
            "recent_activity": {
                "posts_last_7_days": len(recent_posts),
                "platform_distribution": dict(Counter(post.get('platform') for post in recent_posts)),
                "source_distribution": dict(Counter(post.get('source') for post in recent_posts))
            },
            "sample_posts": [
                {
                    "title": post.get('title'),
                    "source": post.get('source'),
                    "platform": post.get('platform'),
                    "date": post.get('date').isoformat() if post.get('date') else None,
                    "url": post.get('url')
                }
                for post in recent_posts[:10]
            ]
        }
        
        # Save report
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Report exported to: {filename}")
        return filename
    
    def run_full_analysis(self):
        """Run complete analysis"""
        try:
            self.get_basic_statistics()
            self.analyze_recent_activity()
            self.analyze_content_keywords()
            self.show_sample_posts(limit=3)
            report_file = self.export_summary_report()
            
            logger.info("\n" + "=" * 50)
            logger.info("✅ Analysis completed successfully!")
            logger.info(f"📄 Report saved as: {report_file}")
            logger.info("=" * 50)
            
        except Exception as e:
            logger.error(f"Analysis failed: {e}")
        finally:
            self.db_manager.close_connection()

def main():
    """Main entry point"""
    analyzer = HarassmentDataAnalyzer()
    analyzer.run_full_analysis()

if __name__ == "__main__":
    main()
