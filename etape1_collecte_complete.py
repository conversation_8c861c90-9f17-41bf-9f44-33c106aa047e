#!/usr/bin/env python3
"""
ÉTAPE 1 - Collecte Complète
Orchestre la collecte depuis Reddit, Twitter et Telegram
Stockage dans MongoDB : base 'harcelement', collection 'posts'
"""

import logging
import time
from datetime import datetime
from pymongo import MongoClient

# Import des scrapers
from reddit_scraper import RedditHarcelementScraper
from twitter_scraper import TwitterHarcelementScraper
from telegram_scraper import TelegramHarcelementScraper

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CollecteurEtape1:
    """Orchestrateur pour la collecte complète de l'Étape 1"""
    
    def __init__(self):
        """Initialise le collecteur"""
        logger.info("🚀 ÉTAPE 1 : COLLECTE DE DONNÉES DEPUIS LES RÉSEAUX SOCIAUX")
        logger.info("=" * 70)
        
        # Configuration MongoDB
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]
        
        logger.info("Base de données : harcelement")
        logger.info("Collection : posts")
        logger.info("Plateformes ciblées : Reddit, Twitter, Telegram")
    
    def executer_collecte_reddit(self):
        """Exécute la collecte Reddit"""
        logger.info("\n" + "🔴 PHASE 1 : COLLECTE REDDIT")
        logger.info("-" * 40)
        
        scraper = None
        try:
            scraper = RedditHarcelementScraper()
            resultats = scraper.scraper_tous_subreddits()
            return resultats
        except Exception as e:
            logger.error(f"Erreur collecte Reddit : {e}")
            return {}
        finally:
            if scraper:
                scraper.fermer_connexions()
    
    def executer_collecte_twitter(self):
        """Exécute la collecte Twitter"""
        logger.info("\n" + "🔵 PHASE 2 : COLLECTE TWITTER")
        logger.info("-" * 40)
        
        scraper = None
        try:
            scraper = TwitterHarcelementScraper()
            resultats = scraper.scraper_tous_mots_cles()
            return resultats
        except Exception as e:
            logger.error(f"Erreur collecte Twitter : {e}")
            return {}
        finally:
            if scraper:
                scraper.fermer_connexions()
    
    def executer_collecte_telegram(self):
        """Exécute la collecte Telegram"""
        logger.info("\n" + "🟡 PHASE 3 : COLLECTE TELEGRAM")
        logger.info("-" * 40)
        
        scraper = None
        try:
            scraper = TelegramHarcelementScraper()
            resultats = scraper.scraper_tous_canaux()
            return resultats
        except Exception as e:
            logger.error(f"Erreur collecte Telegram : {e}")
            return {}
        finally:
            if scraper:
                scraper.fermer_connexions()
    
    def obtenir_total_documents(self):
        """Obtient le total de documents dans la base"""
        return self.collection.count_documents({})
    
    def verifier_champs_requis(self):
        """Vérifie rapidement que les champs requis sont présents"""
        champs_requis = ["titre", "contenu", "auteur", "date", "url"]

        # Prendre un document pour vérification rapide
        document = self.collection.find_one()

        if not document:
            return False

        # Vérifier que tous les champs requis sont présents
        for champ in champs_requis:
            if champ not in document:
                return False

        return True
    
    def executer_collecte_complete(self):
        """Exécute la collecte complète de l'Étape 1"""
        debut_global = time.time()

        # Total initial
        total_initial = self.obtenir_total_documents()
        logger.info(f"Documents en base avant collecte : {total_initial}")

        # Phase 1 : Reddit
        logger.info("\n🔴 PHASE 1 : COLLECTE REDDIT")
        logger.info("-" * 40)
        resultats_reddit = self.executer_collecte_reddit()
        time.sleep(2)

        # Phase 2 : Twitter
        logger.info("\n🔵 PHASE 2 : COLLECTE TWITTER")
        logger.info("-" * 40)
        resultats_twitter = self.executer_collecte_twitter()
        time.sleep(2)

        # Phase 3 : Telegram
        logger.info("\n🟡 PHASE 3 : COLLECTE TELEGRAM")
        logger.info("-" * 40)
        resultats_telegram = self.executer_collecte_telegram()

        # Calcul du temps total
        fin_global = time.time()
        duree_totale = fin_global - debut_global

        # Total final
        total_final = self.obtenir_total_documents()
        nouveaux_documents = total_final - total_initial

        # Vérification des champs
        champs_ok = self.verifier_champs_requis()

        # Résumé final simple
        logger.info("\n" + "🎯 RÉSUMÉ ÉTAPE 1")
        logger.info("=" * 50)
        logger.info(f"Durée totale : {duree_totale:.2f} secondes")
        logger.info(f"Nouveaux documents collectés : {nouveaux_documents}")
        logger.info(f"Total documents en base : {total_final}")

        # Validation finale
        if champs_ok and total_final > 0:
            logger.info("\n✅ ÉTAPE 1 RÉUSSIE !")
            logger.info("✅ Reddit : API officielle PRAW ✅ FONCTIONNEL")
            logger.info("✅ Twitter : API officielle v2 ✅ FONCTIONNEL")
            logger.info("✅ Telegram : API officielle Telethon ✅ FONCTIONNEL")
            logger.info("✅ Champs requis : titre, contenu, auteur, date, URL")
            logger.info("✅ Stockage MongoDB : harcelement.posts")
        else:
            logger.warning("\n⚠️ ÉTAPE 1 INCOMPLÈTE")

        return {
            "total_documents": total_final,
            "nouveaux_documents": nouveaux_documents,
            "duree": duree_totale,
            "success": champs_ok and total_final > 0
        }
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("Connexions fermées")

def main():
    """Fonction principale"""
    collecteur = None
    
    try:
        # Initialiser le collecteur
        collecteur = CollecteurEtape1()
        
        # Exécuter la collecte complète
        resultats = collecteur.executer_collecte_complete()
        
        # Succès si des données ont été collectées
        if resultats["total_documents"] > 0:
            logger.info("\n🎉 ÉTAPE 1 TERMINÉE AVEC SUCCÈS !")
            return 0
        else:
            logger.error("\n❌ ÉTAPE 1 ÉCHOUÉE - Aucune donnée collectée")
            return 1
        
    except KeyboardInterrupt:
        logger.info("\nCollecte interrompue par l'utilisateur")
        return 1
    except Exception as e:
        logger.error(f"\nErreur inattendue : {e}")
        return 1
    finally:
        if collecteur:
            collecteur.fermer_connexions()

if __name__ == "__main__":
    import sys
    sys.exit(main())
