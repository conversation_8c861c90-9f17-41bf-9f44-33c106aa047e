#!/usr/bin/env python3
"""
ÉTAPE 1 - Collecte Complète
Orchestre la collecte depuis Reddit, Twitter et Telegram
Stockage dans MongoDB : base 'harcelement', collection 'posts'
"""

import logging
import time
from datetime import datetime
from pymongo import MongoClient

# Import des scrapers
from reddit_scraper import RedditHarcelementScraper
from twitter_scraper import TwitterHarcelementScraper
from telegram_scraper import TelegramHarcelementScraper

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CollecteurEtape1:
    """Orchestrateur pour la collecte complète de l'Étape 1"""
    
    def __init__(self):
        """Initialise le collecteur"""
        logger.info("🚀 ÉTAPE 1 : COLLECTE DE DONNÉES DEPUIS LES RÉSEAUX SOCIAUX")
        logger.info("=" * 70)
        
        # Configuration MongoDB
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]
        
        logger.info("Base de données : harcelement")
        logger.info("Collection : posts")
        logger.info("Plateformes ciblées : Reddit, Twitter, Telegram")
    
    def executer_collecte_reddit(self):
        """Exécute la collecte Reddit"""
        logger.info("\n" + "🔴 PHASE 1 : COLLECTE REDDIT")
        logger.info("-" * 40)
        
        scraper = None
        try:
            scraper = RedditHarcelementScraper()
            resultats = scraper.scraper_tous_subreddits()
            return resultats
        except Exception as e:
            logger.error(f"Erreur collecte Reddit : {e}")
            return {}
        finally:
            if scraper:
                scraper.fermer_connexions()
    
    def executer_collecte_twitter(self):
        """Exécute la collecte Twitter"""
        logger.info("\n" + "🔵 PHASE 2 : COLLECTE TWITTER")
        logger.info("-" * 40)
        
        scraper = None
        try:
            scraper = TwitterHarcelementScraper()
            resultats = scraper.scraper_tous_mots_cles()
            return resultats
        except Exception as e:
            logger.error(f"Erreur collecte Twitter : {e}")
            return {}
        finally:
            if scraper:
                scraper.fermer_connexions()
    
    def executer_collecte_telegram(self):
        """Exécute la collecte Telegram"""
        logger.info("\n" + "🟡 PHASE 3 : COLLECTE TELEGRAM")
        logger.info("-" * 40)
        
        scraper = None
        try:
            scraper = TelegramHarcelementScraper()
            resultats = scraper.scraper_tous_canaux()
            return resultats
        except Exception as e:
            logger.error(f"Erreur collecte Telegram : {e}")
            return {}
        finally:
            if scraper:
                scraper.fermer_connexions()
    
    def obtenir_statistiques_finales(self):
        """Obtient les statistiques finales de la base"""
        logger.info("\n" + "📊 STATISTIQUES FINALES")
        logger.info("=" * 30)
        
        # Total documents
        total = self.collection.count_documents({})
        logger.info(f"Total documents collectés : {total}")
        
        # Par plateforme
        pipeline_plateforme = [
            {"$group": {"_id": "$plateforme", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        logger.info("\nRépartition par plateforme :")
        for result in self.collection.aggregate(pipeline_plateforme):
            if result['_id']:
                logger.info(f"  {result['_id']}: {result['count']} documents")
        
        # Top sources
        pipeline_source = [
            {"$group": {"_id": "$source", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}},
            {"$limit": 10}
        ]
        
        logger.info("\nTop 10 sources :")
        for result in self.collection.aggregate(pipeline_source):
            logger.info(f"  {result['_id']}: {result['count']} documents")
        
        return total
    
    def verifier_champs_requis(self):
        """Vérifie que les champs requis sont présents"""
        logger.info("\n" + "✅ VÉRIFICATION CHAMPS REQUIS")
        logger.info("-" * 35)
        
        champs_requis = ["titre", "contenu", "auteur", "date", "url"]
        
        # Prendre quelques documents pour vérification
        documents = list(self.collection.find().limit(5))
        
        if not documents:
            logger.warning("Aucun document trouvé pour vérification")
            return False
        
        documents_valides = 0
        
        for i, doc in enumerate(documents):
            logger.info(f"\nDocument {i+1} :")
            document_valide = True
            
            for champ in champs_requis:
                if champ in doc and doc[champ]:
                    valeur = str(doc[champ])
                    if len(valeur) > 50:
                        valeur = valeur[:50] + "..."
                    logger.info(f"  ✓ {champ}: {valeur}")
                else:
                    logger.warning(f"  ✗ {champ}: MANQUANT")
                    document_valide = False
            
            if document_valide:
                documents_valides += 1
        
        pourcentage = (documents_valides / len(documents)) * 100
        logger.info(f"\nRésultat : {documents_valides}/{len(documents)} documents valides ({pourcentage:.1f}%)")
        
        return pourcentage >= 80
    
    def executer_collecte_complete(self):
        """Exécute la collecte complète de l'Étape 1"""
        debut_global = time.time()
        
        # Statistiques initiales
        total_initial = self.collection.count_documents({})
        logger.info(f"Documents en base avant collecte : {total_initial}")
        
        # Phase 1 : Reddit
        resultats_reddit = self.executer_collecte_reddit()
        time.sleep(2)  # Pause entre plateformes
        
        # Phase 2 : Twitter
        resultats_twitter = self.executer_collecte_twitter()
        time.sleep(2)  # Pause entre plateformes
        
        # Phase 3 : Telegram
        resultats_telegram = self.executer_collecte_telegram()
        
        # Calcul du temps total
        fin_global = time.time()
        duree_totale = fin_global - debut_global
        
        # Statistiques finales
        total_final = self.obtenir_statistiques_finales()
        nouveaux_documents = total_final - total_initial
        
        # Vérification des champs
        champs_ok = self.verifier_champs_requis()
        
        # Résumé final
        logger.info("\n" + "🎯 RÉSUMÉ ÉTAPE 1")
        logger.info("=" * 50)
        logger.info(f"Durée totale : {duree_totale:.2f} secondes")
        logger.info(f"Documents avant : {total_initial}")
        logger.info(f"Documents après : {total_final}")
        logger.info(f"Nouveaux documents : {nouveaux_documents}")
        
        # Détail par plateforme
        total_reddit = sum(resultats_reddit.values()) if resultats_reddit else 0
        total_twitter = sum(resultats_twitter.values()) if resultats_twitter else 0
        total_telegram = sum(resultats_telegram.values()) if resultats_telegram else 0
        
        logger.info(f"\nNouveaux par plateforme :")
        logger.info(f"  Reddit : {total_reddit}")
        logger.info(f"  Twitter : {total_twitter}")
        logger.info(f"  Telegram : {total_telegram}")
        
        # Validation
        if champs_ok:
            logger.info("\n✅ ÉTAPE 1 RÉUSSIE !")
            logger.info("✅ Champs requis présents : titre, contenu, auteur, date, URL")
            logger.info("✅ Données stockées dans MongoDB : harcelement.posts")
            logger.info("✅ Collecte multi-plateformes fonctionnelle")
        else:
            logger.warning("\n⚠️ ÉTAPE 1 PARTIELLEMENT RÉUSSIE")
            logger.warning("Certains champs requis sont manquants")
        
        return {
            "total_documents": total_final,
            "nouveaux_documents": nouveaux_documents,
            "reddit": total_reddit,
            "twitter": total_twitter,
            "telegram": total_telegram,
            "duree": duree_totale,
            "champs_valides": champs_ok
        }
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("Connexions fermées")

def main():
    """Fonction principale"""
    collecteur = None
    
    try:
        # Initialiser le collecteur
        collecteur = CollecteurEtape1()
        
        # Exécuter la collecte complète
        resultats = collecteur.executer_collecte_complete()
        
        # Succès si des données ont été collectées
        if resultats["total_documents"] > 0:
            logger.info("\n🎉 ÉTAPE 1 TERMINÉE AVEC SUCCÈS !")
            return 0
        else:
            logger.error("\n❌ ÉTAPE 1 ÉCHOUÉE - Aucune donnée collectée")
            return 1
        
    except KeyboardInterrupt:
        logger.info("\nCollecte interrompue par l'utilisateur")
        return 1
    except Exception as e:
        logger.error(f"\nErreur inattendue : {e}")
        return 1
    finally:
        if collecteur:
            collecteur.fermer_connexions()

if __name__ == "__main__":
    import sys
    sys.exit(main())
