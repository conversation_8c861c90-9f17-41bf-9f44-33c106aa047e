#!/usr/bin/env python3
"""
Configuration d'exemple pour l'analyse du harcèlement en ligne
Copiez ce fichier vers config.py et remplissez vos clés API
"""

# ===== CONFIGURATION BASES DE DONNÉES =====

# MongoDB
MONGODB_CONFIG = {
    "host": "localhost",
    "port": 27017,
    "database": "harcelement",
    "collection": "posts",
    "username": None,  # Optionnel
    "password": None,  # Optionnel
    "auth_source": "admin"  # Base d'authentification
}

# Elasticsearch
ELASTICSEARCH_CONFIG = {
    "host": "localhost",
    "port": 9200,
    "scheme": "http",
    "index_name": "harcelement_posts",
    "username": None,  # Optionnel
    "password": None,  # Optionnel
    "verify_certs": False,  # Pour développement
    "ssl_show_warn": False
}

# Kibana
KIBANA_CONFIG = {
    "host": "localhost",
    "port": 5601,
    "scheme": "http"
}

# ===== CONFIGURATION APIs =====

# Reddit API (PRAW)
# Créer une app sur: https://www.reddit.com/prefs/apps
REDDIT_CONFIG = {
    "client_id": "VOTRE_REDDIT_CLIENT_ID",
    "client_secret": "VOTRE_REDDIT_CLIENT_SECRET",
    "user_agent": "HarcelementAnalysis/1.0 by VotreUsername",
    "username": "votre_username_reddit",  # Optionnel
    "password": "votre_password_reddit"   # Optionnel
}

# Telegram API
# Obtenir sur: https://my.telegram.org
TELEGRAM_CONFIG = {
    "api_id": "VOTRE_TELEGRAM_API_ID",
    "api_hash": "VOTRE_TELEGRAM_API_HASH",
    "phone_number": "+33123456789",  # Votre numéro
    "session_name": "session_telegram"
}

# Twitter API v2
# Créer une app sur: https://developer.twitter.com
TWITTER_CONFIG = {
    "bearer_token": "VOTRE_TWITTER_BEARER_TOKEN",
    "api_key": "VOTRE_TWITTER_API_KEY",          # Optionnel
    "api_secret": "VOTRE_TWITTER_API_SECRET",    # Optionnel
    "access_token": "VOTRE_ACCESS_TOKEN",        # Optionnel
    "access_token_secret": "VOTRE_ACCESS_SECRET" # Optionnel
}

# ===== CONFIGURATION COLLECTE =====

# Sources Reddit
REDDIT_SOURCES = {
    "subreddits": [
        "bullying",
        "cyberbullying", 
        "TrueOffMyChest"
    ],
    "limit_per_subreddit": 50,
    "sort_method": "new",  # new, hot, top
    "time_filter": "week"  # hour, day, week, month, year, all
}

# Sources Telegram
TELEGRAM_SOURCES = {
    "channels": [
        "support_harassment",
        "anti_bullying_help",
        "mental_health_support"
    ],
    "limit_per_channel": 25,
    "days_back": 30
}

# Sources Twitter
TWITTER_SOURCES = {
    "keywords": [
        "bullying",
        "cyberbullying",
        "harassment",
        "harcèlement"
    ],
    "limit_total": 100,
    "lang": ["en", "fr"],
    "result_type": "recent"  # recent, popular, mixed
}

# ===== CONFIGURATION NLP =====

# Analyse de sentiment
SENTIMENT_CONFIG = {
    "vader_weight": 0.6,      # Poids VADER (0.0 à 1.0)
    "textblob_weight": 0.4,   # Poids TextBlob (0.0 à 1.0)
    "negative_threshold": -0.1,  # Seuil sentiment négatif
    "positive_threshold": 0.1,   # Seuil sentiment positif
    "confidence_threshold": 0.7  # Seuil confiance détection langue
}

# Prétraitement
PREPROCESSING_CONFIG = {
    "remove_urls": True,
    "remove_mentions": True,
    "remove_hashtags": False,  # Garder pour contexte
    "remove_emojis": False,    # Garder pour sentiment
    "lowercase": True,
    "remove_punctuation": False,  # Garder pour sentiment
    "min_text_length": 10,
    "max_text_length": 5000
}

# Langues supportées
SUPPORTED_LANGUAGES = {
    "fr": "français",
    "en": "anglais", 
    "es": "espagnol",
    "de": "allemand",
    "it": "italien",
    "pt": "portugais"
}

# ===== CONFIGURATION SYSTÈME =====

# Logging
LOGGING_CONFIG = {
    "level": "INFO",  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "harcelement_analysis.log",
    "max_bytes": 10485760,  # 10MB
    "backup_count": 5
}

# Performance
PERFORMANCE_CONFIG = {
    "batch_size": 100,        # Taille des lots de traitement
    "max_workers": 4,         # Threads parallèles
    "request_delay": 1.0,     # Délai entre requêtes API (secondes)
    "retry_attempts": 3,      # Tentatives en cas d'erreur
    "timeout": 30             # Timeout requêtes (secondes)
}

# Cache
CACHE_CONFIG = {
    "enabled": True,
    "ttl": 3600,             # Time to live (secondes)
    "max_size": 1000,        # Nombre max d'entrées
    "backend": "memory"      # memory, redis, file
}

# ===== CONFIGURATION SÉCURITÉ =====

# Chiffrement
SECURITY_CONFIG = {
    "encryption_key": "VOTRE_CLE_CHIFFREMENT_32_CHARS",
    "jwt_secret": "VOTRE_JWT_SECRET",
    "password_salt": "VOTRE_SALT_UNIQUE",
    "session_timeout": 3600  # Secondes
}

# Rate limiting
RATE_LIMIT_CONFIG = {
    "reddit": {
        "requests_per_minute": 60,
        "requests_per_hour": 3600
    },
    "telegram": {
        "requests_per_minute": 20,
        "requests_per_hour": 1200
    },
    "twitter": {
        "requests_per_minute": 300,
        "requests_per_hour": 18000
    }
}

# ===== CONFIGURATION MONITORING =====

# Métriques
METRICS_CONFIG = {
    "enabled": True,
    "port": 8000,
    "endpoint": "/metrics",
    "collect_system_metrics": True
}

# Alertes
ALERTS_CONFIG = {
    "email_enabled": False,
    "email_smtp": "smtp.gmail.com",
    "email_port": 587,
    "email_user": "<EMAIL>",
    "email_password": "votre_mot_de_passe_app",
    "alert_recipients": ["<EMAIL>"]
}

# ===== CONFIGURATION DÉVELOPPEMENT =====

# Debug
DEBUG_CONFIG = {
    "enabled": False,         # Activer en développement
    "save_raw_data": False,   # Sauvegarder données brutes
    "verbose_logging": False, # Logs détaillés
    "profile_performance": False  # Profiling performance
}

# Tests
TEST_CONFIG = {
    "test_database": "harcelement_test",
    "mock_apis": True,        # Utiliser des mocks en test
    "sample_data_size": 10,   # Taille échantillon test
    "skip_slow_tests": False  # Ignorer tests lents
}

# ===== VALIDATION CONFIGURATION =====

def validate_config():
    """Valide la configuration avant utilisation"""
    errors = []
    
    # Vérifier clés API obligatoires
    if REDDIT_CONFIG["client_id"] == "VOTRE_REDDIT_CLIENT_ID":
        errors.append("Reddit client_id non configuré")
    
    if TELEGRAM_CONFIG["api_id"] == "VOTRE_TELEGRAM_API_ID":
        errors.append("Telegram api_id non configuré")
    
    # Vérifier cohérence poids sentiment
    total_weight = SENTIMENT_CONFIG["vader_weight"] + SENTIMENT_CONFIG["textblob_weight"]
    if abs(total_weight - 1.0) > 0.01:
        errors.append(f"Poids sentiment incohérents: {total_weight} != 1.0")
    
    # Vérifier seuils sentiment
    if SENTIMENT_CONFIG["negative_threshold"] >= SENTIMENT_CONFIG["positive_threshold"]:
        errors.append("Seuils sentiment incohérents")
    
    if errors:
        raise ValueError(f"Erreurs de configuration: {', '.join(errors)}")
    
    return True

# ===== INSTRUCTIONS D'UTILISATION =====

"""
INSTRUCTIONS DE CONFIGURATION:

1. Copiez ce fichier:
   cp config.example.py config.py

2. Éditez config.py avec vos clés API:
   - Reddit: https://www.reddit.com/prefs/apps
   - Telegram: https://my.telegram.org
   - Twitter: https://developer.twitter.com

3. Adaptez les paramètres selon vos besoins:
   - Sources de données
   - Seuils d'analyse
   - Configuration système

4. Validez la configuration:
   python -c "from config import validate_config; validate_config()"

5. Testez la connexion aux APIs:
   python test_apis.py

SÉCURITÉ:
- Ne commitez JAMAIS config.py avec vos vraies clés
- Utilisez des variables d'environnement en production
- Chiffrez les clés sensibles
- Respectez les rate limits des APIs

PRODUCTION:
- Utilisez des variables d'environnement
- Activez le monitoring
- Configurez les alertes
- Sauvegardez régulièrement
"""
