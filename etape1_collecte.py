#!/usr/bin/env python3
"""
ÉTAPE 1 : Collecte de données depuis les réseaux sociaux
Collecte des contenus publics liés au harcèlement depuis Reddit, Twitter et Telegram
"""

import logging
import sys
import time
from datetime import datetime
from typing import Dict, Any

from config import Config
from database import DatabaseManager
from scraper import RedditScraper
from twitter_scraper import TwitterScraper
from telegram_scraper import TelegramScraper

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('etape1_collecte.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class CollecteurDonneesHarcelement:
    """Collecteur de données de harcèlement depuis les réseaux sociaux"""
    
    def __init__(self):
        logger.info("🚀 ÉTAPE 1 : Initialisation du collecteur de données")
        
        # Chargement de la configuration
        self.config = Config()
        if not self.config.validate_config():
            logger.error("❌ Erreur de configuration")
            sys.exit(1)
        
        # Initialisation de la base de données MongoDB
        try:
            self.db_manager = DatabaseManager(self.config)
            logger.info(f"✅ Connexion MongoDB : {self.config.MONGODB_DATABASE}.{self.config.MONGODB_COLLECTION}")
        except Exception as e:
            logger.error(f"❌ Erreur de connexion MongoDB : {e}")
            sys.exit(1)
        
        # Initialisation des scrapers
        self.reddit_scraper = RedditScraper(self.config, self.db_manager)
        self.twitter_scraper = TwitterScraper(self.config, self.db_manager)
        self.telegram_scraper = TelegramScraper(self.config, self.db_manager)
        
        logger.info("✅ Tous les composants initialisés")
    
    def collecter_reddit(self) -> Dict[str, int]:
        """Collecte des données depuis Reddit"""
        logger.info("=" * 60)
        logger.info("📱 COLLECTE REDDIT - Subreddits liés au harcèlement")
        logger.info("=" * 60)
        
        subreddits_cibles = ["bullying", "TrueOffMyChest", "cyberbullying"]
        logger.info(f"Subreddits ciblés : {', '.join([f'r/{s}' for s in subreddits_cibles])}")
        
        debut = time.time()
        resultats = {}
        
        for subreddit in subreddits_cibles:
            logger.info(f"\n🔍 Collecte de r/{subreddit}...")
            posts_ajoutes = self.reddit_scraper.scrape_subreddit(subreddit, self.config.REDDIT_LIMIT_POSTS)
            resultats[f"r/{subreddit}"] = posts_ajoutes
            
            # Pause entre les subreddits
            time.sleep(self.config.RATE_LIMIT_DELAY)
        
        fin = time.time()
        total_posts = sum(resultats.values())
        
        logger.info(f"\n✅ REDDIT - Collecte terminée en {fin - debut:.2f} secondes")
        logger.info(f"📊 Total nouveaux posts : {total_posts}")
        for source, count in resultats.items():
            logger.info(f"   {source}: {count} posts")
        
        return resultats
    
    def collecter_twitter(self) -> Dict[str, int]:
        """Collecte des données depuis Twitter/X"""
        logger.info("=" * 60)
        logger.info("🐦 COLLECTE TWITTER - Hashtags et mots-clés harcèlement")
        logger.info("=" * 60)
        
        if not self.twitter_scraper.is_available():
            logger.warning("⚠️ Twitter API non configuré - collecte ignorée")
            return {}
        
        mots_cles = ["harassment", "bullying", "cyberbullying", "online harassment"]
        logger.info(f"Mots-clés ciblés : {', '.join(mots_cles)}")
        
        debut = time.time()
        resultats = {}
        
        for mot_cle in mots_cles:
            logger.info(f"\n🔍 Recherche Twitter : '{mot_cle}'...")
            query = f'"{mot_cle}" -is:retweet lang:fr OR lang:en'
            tweets_ajoutes = self.twitter_scraper.search_tweets(query, self.config.TWITTER_LIMIT_TWEETS)
            resultats[f"Twitter:{mot_cle}"] = tweets_ajoutes
            
            # Pause entre les recherches
            time.sleep(self.config.RATE_LIMIT_DELAY)
        
        fin = time.time()
        total_tweets = sum(resultats.values())
        
        logger.info(f"\n✅ TWITTER - Collecte terminée en {fin - debut:.2f} secondes")
        logger.info(f"📊 Total nouveaux tweets : {total_tweets}")
        for source, count in resultats.items():
            logger.info(f"   {source}: {count} tweets")
        
        return resultats
    
    def collecter_telegram(self) -> Dict[str, int]:
        """Collecte des données depuis Telegram"""
        logger.info("=" * 60)
        logger.info("📱 COLLECTE TELEGRAM - Groupes publics harcèlement")
        logger.info("=" * 60)
        
        if not self.telegram_scraper.is_available():
            logger.warning("⚠️ Telegram API non configuré - collecte ignorée")
            return {}
        
        if not self.config.TELEGRAM_CHANNELS:
            logger.warning("⚠️ Aucun canal Telegram configuré")
            return {}
        
        logger.info(f"Canaux ciblés : {', '.join(self.config.TELEGRAM_CHANNELS)}")
        
        debut = time.time()
        resultats = self.telegram_scraper.run_scraping()
        fin = time.time()
        
        total_messages = sum(resultats.values())
        
        logger.info(f"\n✅ TELEGRAM - Collecte terminée en {fin - debut:.2f} secondes")
        logger.info(f"📊 Total nouveaux messages : {total_messages}")
        for canal, count in resultats.items():
            logger.info(f"   {canal}: {count} messages")
        
        return resultats
    
    def executer_collecte_complete(self) -> Dict[str, Any]:
        """Exécute la collecte complète depuis toutes les plateformes"""
        logger.info("🎯 DÉBUT DE LA COLLECTE DE DONNÉES - ÉTAPE 1")
        logger.info(f"⏰ Démarrage : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        debut_global = time.time()
        resultats = {
            "reddit": {},
            "twitter": {},
            "telegram": {},
            "resume": {}
        }
        
        # Collecte Reddit
        try:
            resultats["reddit"] = self.collecter_reddit()
        except Exception as e:
            logger.error(f"❌ Erreur collecte Reddit : {e}")
            resultats["reddit"] = {}
        
        # Collecte Twitter
        try:
            resultats["twitter"] = self.collecter_twitter()
        except Exception as e:
            logger.error(f"❌ Erreur collecte Twitter : {e}")
            resultats["twitter"] = {}
        
        # Collecte Telegram
        try:
            resultats["telegram"] = self.collecter_telegram()
        except Exception as e:
            logger.error(f"❌ Erreur collecte Telegram : {e}")
            resultats["telegram"] = {}
        
        # Calcul du résumé
        fin_global = time.time()
        duree_totale = fin_global - debut_global
        
        resultats["resume"] = {
            "total_posts_reddit": sum(resultats["reddit"].values()),
            "total_tweets_twitter": sum(resultats["twitter"].values()),
            "total_messages_telegram": sum(resultats["telegram"].values()),
            "duree_totale_secondes": duree_totale,
            "timestamp_collecte": datetime.now().isoformat()
        }
        
        # Affichage du résumé final
        self._afficher_resume_final(resultats)
        
        return resultats
    
    def _afficher_resume_final(self, resultats: Dict[str, Any]):
        """Affiche le résumé final de la collecte"""
        logger.info("=" * 70)
        logger.info("📊 RÉSUMÉ FINAL - ÉTAPE 1 : COLLECTE DE DONNÉES")
        logger.info("=" * 70)
        
        resume = resultats["resume"]
        total_elements = (resume["total_posts_reddit"] + 
                         resume["total_tweets_twitter"] + 
                         resume["total_messages_telegram"])
        
        logger.info(f"📱 Reddit posts    : {resume['total_posts_reddit']}")
        logger.info(f"🐦 Twitter tweets  : {resume['total_tweets_twitter']}")
        logger.info(f"📱 Telegram msgs   : {resume['total_messages_telegram']}")
        logger.info(f"📊 TOTAL COLLECTÉ  : {total_elements} éléments")
        logger.info(f"⏱️ Durée totale    : {resume['duree_totale_secondes']:.2f} secondes")
        
        # Statistiques de la base de données
        try:
            stats_db = self.db_manager.get_statistics()
            logger.info(f"💾 Total en base   : {stats_db.get('total_posts', 0)} documents")
            
            logger.info("\n📈 Répartition par plateforme :")
            for plateforme, count in stats_db.get('platforms', {}).items():
                if plateforme:
                    logger.info(f"   {plateforme}: {count} documents")
        except Exception as e:
            logger.error(f"Erreur stats DB : {e}")
        
        logger.info("=" * 70)
        logger.info("✅ ÉTAPE 1 TERMINÉE AVEC SUCCÈS !")
        logger.info("=" * 70)
    
    def sauvegarder_resultats(self, resultats: Dict[str, Any]):
        """Sauvegarde les résultats dans un fichier JSON"""
        import json
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        nom_fichier = f"etape1_resultats_{timestamp}.json"
        
        with open(nom_fichier, 'w', encoding='utf-8') as f:
            json.dump(resultats, f, indent=2, default=str, ensure_ascii=False)
        
        logger.info(f"💾 Résultats sauvegardés : {nom_fichier}")
        return nom_fichier
    
    def nettoyer(self):
        """Nettoyage des ressources"""
        if self.db_manager:
            self.db_manager.close_connection()
        logger.info("🧹 Nettoyage terminé")

def main():
    """Point d'entrée principal"""
    collecteur = None
    
    try:
        # Initialisation
        collecteur = CollecteurDonneesHarcelement()
        
        # Exécution de la collecte
        resultats = collecteur.executer_collecte_complete()
        
        # Sauvegarde des résultats
        fichier_resultats = collecteur.sauvegarder_resultats(resultats)
        
        logger.info(f"🎉 ÉTAPE 1 RÉUSSIE ! Résultats dans : {fichier_resultats}")
        return 0
        
    except KeyboardInterrupt:
        logger.info("⏹️ Collecte interrompue par l'utilisateur")
        return 1
    except Exception as e:
        logger.error(f"❌ Erreur inattendue : {e}")
        return 1
    finally:
        if collecteur:
            collecteur.nettoyer()

if __name__ == "__main__":
    sys.exit(main())
