#!/usr/bin/env python3
"""
Script principal pour exécuter le pipeline complet d'analyse du harcèlement
Orchestre toutes les étapes du traitement des données
"""

import sys
import time
import logging
import argparse
from datetime import datetime
from pathlib import Path

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('pipeline.log')
    ]
)
logger = logging.getLogger(__name__)

class PipelineRunner:
    """Orchestrateur du pipeline d'analyse"""
    
    def __init__(self, config_file=None):
        """Initialise le runner de pipeline"""
        self.config_file = config_file
        self.start_time = None
        self.steps_completed = []
        self.steps_failed = []
        
        logger.info("🚀 Initialisation du Pipeline d'Analyse du Harcèlement")
        logger.info("=" * 70)
    
    def validate_environment(self):
        """Valide l'environnement avant exécution"""
        logger.info("🔍 Validation de l'environnement...")
        
        try:
            # Importer le script de test
            from test_configuration import (
                test_python_version,
                test_dependencies,
                test_mongodb_connection
            )
            
            # Tests critiques
            critical_tests = [
                ("Python", test_python_version),
                ("Dépendances", test_dependencies),
                ("MongoDB", test_mongodb_connection)
            ]
            
            for test_name, test_func in critical_tests:
                if not test_func():
                    logger.error(f"❌ Test critique échoué: {test_name}")
                    return False
            
            logger.info("✅ Environnement validé")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur validation environnement: {e}")
            return False
    
    def run_step(self, step_name, script_path, description):
        """Exécute une étape du pipeline"""
        logger.info(f"\n{'='*20} ÉTAPE: {step_name} {'='*20}")
        logger.info(f"📝 {description}")
        logger.info(f"🔧 Script: {script_path}")
        
        step_start = time.time()
        
        try:
            # Vérifier que le script existe
            if not Path(script_path).exists():
                raise FileNotFoundError(f"Script non trouvé: {script_path}")
            
            # Exécuter le script
            import subprocess
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                timeout=600  # 10 minutes max par étape
            )
            
            step_duration = time.time() - step_start
            
            if result.returncode == 0:
                logger.info(f"✅ {step_name} terminée avec succès")
                logger.info(f"⏱️ Durée: {step_duration:.1f} secondes")
                self.steps_completed.append((step_name, step_duration))
                
                # Afficher les dernières lignes de sortie
                if result.stdout:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[-5:]:  # 5 dernières lignes
                        if line.strip():
                            logger.info(f"📄 {line}")
                
                return True
            else:
                logger.error(f"❌ {step_name} a échoué (code: {result.returncode})")
                if result.stderr:
                    logger.error(f"Erreur: {result.stderr}")
                self.steps_failed.append((step_name, result.stderr))
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"❌ {step_name} a dépassé le timeout (10 minutes)")
            self.steps_failed.append((step_name, "Timeout"))
            return False
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'exécution de {step_name}: {e}")
            self.steps_failed.append((step_name, str(e)))
            return False
    
    def run_complete_pipeline(self, skip_steps=None):
        """Exécute le pipeline complet"""
        self.start_time = time.time()
        skip_steps = skip_steps or []
        
        logger.info("🎯 DÉBUT DU PIPELINE COMPLET")
        logger.info(f"🕐 Heure de début: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Définition des étapes
        pipeline_steps = [
            {
                "name": "ÉTAPE 1 - Collecte",
                "script": "etape1_collecte_complete.py",
                "description": "Collecte des données depuis Reddit, Telegram et Twitter"
            },
            {
                "name": "ÉTAPE 2 - Prétraitement", 
                "script": "etape2_pretraitement.py",
                "description": "Nettoyage et normalisation des données textuelles"
            },
            {
                "name": "ÉTAPE 3 - Analyse NLP",
                "script": "etape3_nlp.py", 
                "description": "Analyse de sentiment et classification émotionnelle"
            },
            {
                "name": "ÉTAPE 4 - Indexation",
                "script": "etape4_elasticsearch_simulation.py",
                "description": "Indexation des données dans Elasticsearch (simulation)"
            },
            {
                "name": "ÉTAPE 5 - Dashboard",
                "script": "etape5_kibana_dashboard.py",
                "description": "Génération du tableau de bord Kibana"
            }
        ]
        
        # Exécution séquentielle
        for i, step in enumerate(pipeline_steps, 1):
            step_id = f"etape{i}"
            
            if step_id in skip_steps:
                logger.info(f"⏭️ Étape {i} ignorée: {step['name']}")
                continue
            
            success = self.run_step(
                step['name'],
                step['script'], 
                step['description']
            )
            
            if not success:
                logger.error(f"❌ Pipeline arrêté à l'étape {i}")
                return False
        
        return True
    
    def generate_report(self):
        """Génère un rapport de fin d'exécution"""
        total_duration = time.time() - self.start_time if self.start_time else 0
        
        logger.info("\n" + "="*70)
        logger.info("📊 RAPPORT D'EXÉCUTION DU PIPELINE")
        logger.info("="*70)
        
        logger.info(f"🕐 Heure de fin: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"⏱️ Durée totale: {total_duration:.1f} secondes ({total_duration/60:.1f} minutes)")
        
        # Étapes réussies
        if self.steps_completed:
            logger.info(f"\n✅ ÉTAPES RÉUSSIES ({len(self.steps_completed)}):")
            for step_name, duration in self.steps_completed:
                logger.info(f"   ✓ {step_name} ({duration:.1f}s)")
        
        # Étapes échouées
        if self.steps_failed:
            logger.info(f"\n❌ ÉTAPES ÉCHOUÉES ({len(self.steps_failed)}):")
            for step_name, error in self.steps_failed:
                logger.info(f"   ✗ {step_name}: {error}")
        
        # Statistiques
        total_steps = len(self.steps_completed) + len(self.steps_failed)
        success_rate = (len(self.steps_completed) / total_steps * 100) if total_steps > 0 else 0
        
        logger.info(f"\n📈 STATISTIQUES:")
        logger.info(f"   Taux de réussite: {success_rate:.1f}%")
        logger.info(f"   Étapes réussies: {len(self.steps_completed)}")
        logger.info(f"   Étapes échouées: {len(self.steps_failed)}")
        
        # Résultat final
        if len(self.steps_failed) == 0:
            logger.info("\n🎉 PIPELINE TERMINÉ AVEC SUCCÈS !")
            logger.info("✅ Toutes les étapes ont été exécutées correctement")
            logger.info("📊 Les données sont prêtes pour l'analyse dans Kibana")
            return True
        else:
            logger.warning(f"\n⚠️ PIPELINE TERMINÉ AVEC {len(self.steps_failed)} ERREUR(S)")
            logger.info("💡 Consultez les logs ci-dessus pour résoudre les problèmes")
            return False
    
    def run_single_step(self, step_number):
        """Exécute une seule étape du pipeline"""
        steps_map = {
            1: ("ÉTAPE 1 - Collecte", "etape1_collecte_complete.py", 
                "Collecte des données depuis Reddit, Telegram et Twitter"),
            2: ("ÉTAPE 2 - Prétraitement", "etape2_pretraitement.py",
                "Nettoyage et normalisation des données textuelles"),
            3: ("ÉTAPE 3 - Analyse NLP", "etape3_nlp.py",
                "Analyse de sentiment et classification émotionnelle"),
            4: ("ÉTAPE 4 - Indexation", "etape4_elasticsearch_simulation.py",
                "Indexation des données dans Elasticsearch (simulation)"),
            5: ("ÉTAPE 5 - Dashboard", "etape5_kibana_dashboard.py",
                "Génération du tableau de bord Kibana")
        }
        
        if step_number not in steps_map:
            logger.error(f"❌ Étape {step_number} invalide (1-5)")
            return False
        
        self.start_time = time.time()
        name, script, description = steps_map[step_number]
        
        success = self.run_step(name, script, description)
        self.generate_report()
        
        return success

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(
        description="Pipeline d'analyse du harcèlement en ligne",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemples d'utilisation:
  python run_pipeline.py                    # Pipeline complet
  python run_pipeline.py --step 3           # Étape 3 uniquement
  python run_pipeline.py --skip etape1,etape2  # Ignorer étapes 1 et 2
  python run_pipeline.py --validate-only    # Validation uniquement
  python run_pipeline.py --config config.py # Fichier config personnalisé
        """
    )
    
    parser.add_argument(
        '--step', 
        type=int, 
        choices=[1, 2, 3, 4, 5],
        help='Exécuter une seule étape (1-5)'
    )
    
    parser.add_argument(
        '--skip',
        type=str,
        help='Étapes à ignorer (ex: etape1,etape3)'
    )
    
    parser.add_argument(
        '--validate-only',
        action='store_true',
        help='Valider l\'environnement uniquement'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='Fichier de configuration personnalisé'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Logs détaillés'
    )
    
    args = parser.parse_args()
    
    # Configuration logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Initialiser le runner
        runner = PipelineRunner(config_file=args.config)
        
        # Validation environnement
        if not runner.validate_environment():
            logger.error("❌ Validation environnement échouée")
            sys.exit(1)
        
        if args.validate_only:
            logger.info("✅ Validation terminée avec succès")
            sys.exit(0)
        
        # Exécution
        if args.step:
            # Étape unique
            success = runner.run_single_step(args.step)
        else:
            # Pipeline complet
            skip_steps = args.skip.split(',') if args.skip else []
            success = runner.run_complete_pipeline(skip_steps=skip_steps)
            runner.generate_report()
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ Pipeline interrompu par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
