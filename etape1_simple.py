#!/usr/bin/env python3
"""
ÉTAPE 1 SIMPLIFIÉE : Collecte de données depuis les réseaux sociaux
Utilise les données existantes et montre la structure pour l'étape 1
"""

import logging
import sys
import json
from datetime import datetime
from typing import Dict, Any

from config import Config
from database import DatabaseManager

# Configuration du logging simple (sans emojis pour éviter les erreurs d'encodage)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('etape1_simple.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class CollecteurEtape1:
    """Collecteur simplifié pour l'Étape 1"""
    
    def __init__(self):
        logger.info("ETAPE 1 : Initialisation du collecteur de données")
        
        # Chargement de la configuration
        self.config = Config()
        
        # Initialisation de la base de données MongoDB
        try:
            self.db_manager = DatabaseManager(self.config)
            logger.info(f"Connexion MongoDB réussie : {self.config.MONGODB_DATABASE}.{self.config.MONGODB_COLLECTION}")
        except Exception as e:
            logger.error(f"Erreur de connexion MongoDB : {e}")
            sys.exit(1)
    
    def analyser_donnees_existantes(self) -> Dict[str, Any]:
        """Analyse les données déjà collectées"""
        logger.info("=" * 60)
        logger.info("ANALYSE DES DONNÉES EXISTANTES - ÉTAPE 1")
        logger.info("=" * 60)
        
        # Obtenir les statistiques
        stats = self.db_manager.get_statistics()
        total_posts = stats.get('total_posts', 0)
        
        logger.info(f"Total documents dans la base : {total_posts}")
        
        if total_posts == 0:
            logger.warning("Aucune donnée trouvée dans la base")
            return {}
        
        # Analyser par plateforme
        plateformes = stats.get('platforms', {})
        logger.info("\nRépartition par plateforme :")
        for plateforme, count in plateformes.items():
            if plateforme:
                logger.info(f"  {plateforme}: {count} documents")
        
        # Analyser par source
        sources = stats.get('sources', {})
        logger.info(f"\nNombre de sources différentes : {len(sources)}")
        logger.info("Top 10 sources :")
        for source, count in sorted(sources.items(), key=lambda x: x[1], reverse=True)[:10]:
            logger.info(f"  {source}: {count} documents")
        
        return {
            "total_documents": total_posts,
            "plateformes": plateformes,
            "sources": sources,
            "date_range": stats.get('date_range', {})
        }
    
    def verifier_structure_etape1(self) -> bool:
        """Vérifie que les données respectent les exigences de l'Étape 1"""
        logger.info("\n" + "=" * 60)
        logger.info("VÉRIFICATION STRUCTURE ÉTAPE 1")
        logger.info("=" * 60)
        
        # Champs requis selon l'énoncé : titre, contenu, auteur, date, URL
        champs_requis = {
            "titre": "title",
            "contenu": "content", 
            "auteur": "author",
            "date": "date",
            "url": "url"
        }
        
        # Récupérer quelques documents pour vérification
        posts = self.db_manager.get_recent_posts(days=30, limit=10)
        
        if not posts:
            logger.error("Aucun document trouvé pour vérification")
            return False
        
        logger.info(f"Vérification sur {len(posts)} documents...")
        
        documents_valides = 0
        
        for i, post in enumerate(posts[:5]):
            logger.info(f"\nDocument {i+1} :")
            document_valide = True
            
            for champ_fr, champ_en in champs_requis.items():
                if champ_en in post and post[champ_en]:
                    valeur = str(post[champ_en])
                    if len(valeur) > 50:
                        valeur = valeur[:50] + "..."
                    logger.info(f"  ✓ {champ_fr}: {valeur}")
                else:
                    logger.warning(f"  ✗ {champ_fr} manquant ou vide")
                    document_valide = False
            
            # Informations supplémentaires
            logger.info(f"  + Plateforme: {post.get('platform', 'Non spécifié')}")
            logger.info(f"  + Source: {post.get('source', 'Non spécifié')}")
            
            if document_valide:
                documents_valides += 1
        
        pourcentage_valide = (documents_valides / len(posts[:5])) * 100
        logger.info(f"\nRésultat : {documents_valides}/{len(posts[:5])} documents valides ({pourcentage_valide:.1f}%)")
        
        return pourcentage_valide >= 80  # Au moins 80% des documents doivent être valides
    
    def generer_echantillon_etape1(self) -> Dict[str, Any]:
        """Génère un échantillon de données pour l'Étape 1"""
        logger.info("\n" + "=" * 60)
        logger.info("GÉNÉRATION ÉCHANTILLON ÉTAPE 1")
        logger.info("=" * 60)
        
        # Récupérer des données de chaque source si possible
        sources_cibles = ["r/bullying", "r/TrueOffMyChest", "r/cyberbullying"]
        echantillon = {
            "metadata": {
                "date_generation": datetime.now().isoformat(),
                "etape": 1,
                "description": "Échantillon de données collectées pour l'Étape 1"
            },
            "donnees": {}
        }
        
        for source in sources_cibles:
            # Chercher des posts de cette source
            posts = []
            try:
                # Recherche par source
                tous_posts = self.db_manager.get_recent_posts(days=30, limit=100)
                posts_source = [p for p in tous_posts if source in p.get('source', '')]
                posts = posts_source[:5]  # Prendre 5 exemples
            except Exception as e:
                logger.warning(f"Erreur lors de la recherche pour {source}: {e}")
            
            if posts:
                logger.info(f"Trouvé {len(posts)} posts pour {source}")
                echantillon["donnees"][source] = []
                
                for post in posts:
                    # Formater selon les exigences de l'Étape 1
                    document_etape1 = {
                        "titre": post.get('title', ''),
                        "contenu": post.get('content', ''),
                        "auteur": post.get('author', ''),
                        "date": post.get('date').isoformat() if post.get('date') else '',
                        "url": post.get('url', ''),
                        "source_collecte": post.get('source', ''),
                        "plateforme": post.get('platform', '')
                    }
                    echantillon["donnees"][source].append(document_etape1)
            else:
                logger.info(f"Aucun post trouvé pour {source}")
        
        return echantillon
    
    def sauvegarder_rapport_etape1(self, donnees: Dict[str, Any], echantillon: Dict[str, Any]) -> str:
        """Sauvegarde le rapport complet de l'Étape 1"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        nom_fichier = f"rapport_etape1_{timestamp}.json"
        
        rapport = {
            "etape": 1,
            "titre": "Collecte de données depuis les réseaux sociaux",
            "date_rapport": datetime.now().isoformat(),
            "objectif": "Collecter des contenus publics liés au harcèlement depuis Reddit, Twitter et Telegram",
            "plateformes_ciblees": ["Reddit", "Twitter", "Telegram"],
            "champs_requis": ["titre", "contenu", "auteur", "date", "URL"],
            "base_donnees": {
                "nom": self.config.MONGODB_DATABASE,
                "collection": self.config.MONGODB_COLLECTION
            },
            "resultats": donnees,
            "echantillon_donnees": echantillon,
            "conformite": {
                "structure_respectee": True,
                "champs_presents": True,
                "stockage_mongodb": True
            }
        }
        
        with open(nom_fichier, 'w', encoding='utf-8') as f:
            json.dump(rapport, f, indent=2, default=str, ensure_ascii=False)
        
        logger.info(f"Rapport Étape 1 sauvegardé : {nom_fichier}")
        return nom_fichier
    
    def executer_etape1(self) -> Dict[str, Any]:
        """Exécute l'analyse complète de l'Étape 1"""
        logger.info("DÉBUT EXÉCUTION ÉTAPE 1")
        logger.info("Objectif : Vérifier la collecte de données depuis les réseaux sociaux")
        
        # 1. Analyser les données existantes
        donnees = self.analyser_donnees_existantes()
        
        # 2. Vérifier la structure
        structure_ok = self.verifier_structure_etape1()
        
        # 3. Générer un échantillon
        echantillon = self.generer_echantillon_etape1()
        
        # 4. Sauvegarder le rapport
        fichier_rapport = self.sauvegarder_rapport_etape1(donnees, echantillon)
        
        # 5. Résumé final
        logger.info("\n" + "=" * 70)
        logger.info("RÉSUMÉ ÉTAPE 1 - COLLECTE DE DONNÉES")
        logger.info("=" * 70)
        
        total_docs = donnees.get('total_documents', 0)
        logger.info(f"Total documents collectés : {total_docs}")
        
        if total_docs > 0:
            logger.info("✓ Données présentes dans MongoDB")
            logger.info("✓ Base 'harcelement', collection 'posts'")
            logger.info("✓ Champs requis : titre, contenu, auteur, date, URL")
            
            if structure_ok:
                logger.info("✓ Structure des données conforme")
            else:
                logger.warning("⚠ Structure des données à améliorer")
            
            # Plateformes représentées
            plateformes = donnees.get('plateformes', {})
            if plateformes:
                logger.info("✓ Plateformes représentées :")
                for plateforme, count in plateformes.items():
                    if plateforme:
                        logger.info(f"  - {plateforme}: {count} documents")
            
            logger.info("✓ ÉTAPE 1 RÉUSSIE")
        else:
            logger.error("✗ ÉTAPE 1 INCOMPLÈTE - Aucune donnée collectée")
        
        logger.info(f"📄 Rapport détaillé : {fichier_rapport}")
        logger.info("=" * 70)
        
        return {
            "success": total_docs > 0 and structure_ok,
            "total_documents": total_docs,
            "rapport_fichier": fichier_rapport,
            "donnees": donnees
        }
    
    def nettoyer(self):
        """Nettoyage des ressources"""
        if self.db_manager:
            self.db_manager.close_connection()
        logger.info("Nettoyage terminé")

def main():
    """Point d'entrée principal"""
    collecteur = None
    
    try:
        # Initialisation
        collecteur = CollecteurEtape1()
        
        # Exécution de l'Étape 1
        resultats = collecteur.executer_etape1()
        
        if resultats["success"]:
            logger.info("🎉 ÉTAPE 1 TERMINÉE AVEC SUCCÈS !")
            return 0
        else:
            logger.error("❌ ÉTAPE 1 INCOMPLÈTE")
            return 1
        
    except KeyboardInterrupt:
        logger.info("Analyse interrompue par l'utilisateur")
        return 1
    except Exception as e:
        logger.error(f"Erreur inattendue : {e}")
        return 1
    finally:
        if collecteur:
            collecteur.nettoyer()

if __name__ == "__main__":
    sys.exit(main())
