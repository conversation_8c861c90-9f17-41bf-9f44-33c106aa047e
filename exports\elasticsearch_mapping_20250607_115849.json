{"metadata": {"export_date": "2025-06-07T11:58:49.216184", "index_name": "harcelement_posts", "elasticsearch_version": "8.0.0"}, "mapping": {"properties": {"titre": {"type": "text", "analyzer": "standard", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "contenu": {"type": "text", "analyzer": "standard"}, "auteur": {"type": "keyword"}, "date": {"type": "date", "format": "strict_date_optional_time||epoch_millis"}, "url": {"type": "keyword", "index": false}, "langue": {"type": "keyword"}, "sentiment": {"type": "keyword"}, "score": {"type": "float"}, "langue_nom": {"type": "keyword"}, "plateforme": {"type": "keyword"}, "source": {"type": "keyword"}, "sentiment_vader": {"properties": {"compound": {"type": "float"}, "pos": {"type": "float"}, "neu": {"type": "float"}, "neg": {"type": "float"}}}, "sentiment_textblob": {"properties": {"polarity": {"type": "float"}, "subjectivity": {"type": "float"}}}, "date_indexation": {"type": "date"}}}, "settings": {"number_of_shards": 1, "number_of_replicas": 0, "analysis": {"analyzer": {"custom_text_analyzer": {"type": "custom", "tokenizer": "standard", "filter": ["lowercase", "stop"]}}}}}