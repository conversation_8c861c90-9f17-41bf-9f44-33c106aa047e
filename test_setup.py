#!/usr/bin/env python3
"""
Test script to verify the setup and configuration
"""

import sys
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test if all required modules can be imported"""
    logger.info("Testing imports...")
    
    try:
        from config import Config
        logger.info("✅ Config module imported successfully")
    except ImportError as e:
        logger.error(f"❌ Failed to import Config: {e}")
        return False
    
    try:
        from database import DatabaseManager
        logger.info("✅ DatabaseManager imported successfully")
    except ImportError as e:
        logger.error(f"❌ Failed to import DatabaseManager: {e}")
        return False
    
    try:
        from scraper import RedditScraper
        logger.info("✅ RedditScraper imported successfully")
    except ImportError as e:
        logger.error(f"❌ Failed to import RedditScraper: {e}")
        return False
    
    try:
        from twitter_scraper import TwitterScraper
        logger.info("✅ TwitterScraper imported successfully")
    except ImportError as e:
        logger.error(f"❌ Failed to import TwitterScraper: {e}")
        return False
    
    try:
        from telegram_scraper import TelegramScraper
        logger.info("✅ TelegramScraper imported successfully")
    except ImportError as e:
        logger.error(f"❌ Failed to import TelegramScraper: {e}")
        return False
    
    return True

def test_dependencies():
    """Test if all required dependencies are available"""
    logger.info("Testing dependencies...")
    
    dependencies = [
        ('praw', 'Reddit API'),
        ('pymongo', 'MongoDB'),
        ('tweepy', 'Twitter API'),
        ('dotenv', 'Environment variables')
    ]
    
    all_good = True
    
    for module, description in dependencies:
        try:
            __import__(module)
            logger.info(f"✅ {description} ({module}) available")
        except ImportError:
            logger.warning(f"⚠️  {description} ({module}) not available")
            if module in ['praw', 'pymongo', 'dotenv']:
                all_good = False
    
    # Test optional dependencies
    optional_deps = [
        ('telethon', 'Telegram API (optional)')
    ]
    
    for module, description in optional_deps:
        try:
            __import__(module)
            logger.info(f"✅ {description} ({module}) available")
        except ImportError:
            logger.info(f"ℹ️  {description} ({module}) not available (optional)")
    
    return all_good

def test_configuration():
    """Test configuration loading"""
    logger.info("Testing configuration...")
    
    try:
        from config import Config
        config = Config()
        
        # Test basic config loading
        logger.info(f"✅ MongoDB URI: {config.MONGODB_URI}")
        logger.info(f"✅ Database: {config.MONGODB_DATABASE}")
        logger.info(f"✅ Collection: {config.MONGODB_COLLECTION}")
        
        # Test Reddit config
        if config.REDDIT_CLIENT_ID and config.REDDIT_CLIENT_SECRET:
            logger.info("✅ Reddit API credentials configured")
        else:
            logger.warning("⚠️  Reddit API credentials missing")
        
        # Test Twitter config
        if config.TWITTER_BEARER_TOKEN or config.TWITTER_API_KEY:
            logger.info("✅ Twitter API credentials configured")
        else:
            logger.info("ℹ️  Twitter API credentials not configured (optional)")
        
        # Test Telegram config
        if config.TELEGRAM_API_ID and config.TELEGRAM_API_HASH:
            logger.info("✅ Telegram API credentials configured")
        else:
            logger.info("ℹ️  Telegram API credentials not configured (optional)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False

def test_database_connection():
    """Test database connection"""
    logger.info("Testing database connection...")
    
    try:
        from config import Config
        from database import DatabaseManager
        
        config = Config()
        db_manager = DatabaseManager(config)
        
        # Test basic operations
        stats = db_manager.get_statistics()
        logger.info(f"✅ Database connected successfully")
        logger.info(f"✅ Total posts in database: {stats.get('total_posts', 0)}")
        
        db_manager.close_connection()
        return True
        
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        logger.error("   Make sure MongoDB is running and accessible")
        return False

def test_reddit_api():
    """Test Reddit API connection"""
    logger.info("Testing Reddit API...")
    
    try:
        from config import Config
        from database import DatabaseManager
        from scraper import RedditScraper
        
        config = Config()
        db_manager = DatabaseManager(config)
        reddit_scraper = RedditScraper(config, db_manager)
        
        # Test API connection by accessing a subreddit
        subreddit = reddit_scraper.reddit.subreddit('test')
        subreddit.display_name  # This will trigger API call
        
        logger.info("✅ Reddit API connection successful")
        db_manager.close_connection()
        return True
        
    except Exception as e:
        logger.error(f"❌ Reddit API test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🧪 Starting setup verification tests")
    logger.info("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Dependencies Test", test_dependencies),
        ("Configuration Test", test_configuration),
        ("Database Connection Test", test_database_connection),
        ("Reddit API Test", test_reddit_api)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Your setup is ready.")
        return 0
    else:
        logger.error("⚠️  Some tests failed. Please check the configuration and dependencies.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
