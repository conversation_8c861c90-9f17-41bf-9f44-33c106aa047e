#!/usr/bin/env python3
"""
ÉTAPE 1 - Telegram Scraper
Collecte des données de harcèlement depuis Telegram
Stockage dans MongoDB : base 'harcelement', collection 'posts'
"""

from pymongo import MongoClient
from datetime import datetime, timezone
import time
import logging
import asyncio

# Essayer d'importer Telethon
try:
    from telethon import TelegramClient
    from telethon.errors import SessionPasswordNeededError, FloodWaitError
    TELETHON_AVAILABLE = True
except ImportError:
    TELETHON_AVAILABLE = False
    TelegramClient = None

    logger = logging.getLogger(__name__)
    logger.warning("Telethon non installé. Utilisez: pip install telethon")

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TelegramHarcelementScraper:
    def __init__(self):
        """Initialise le scraper Telegram"""
        # Configuration MongoDB (même base que Reddit et Twitter)
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]

        # Configuration Telegram API - Clés intégrées
        self.api_id = "22782850"
        self.api_hash = "6e9fa3a73e7e77905f4d65a7bf5c1e08"
        self.phone = "+21629306382"

        # Canaux/groupes ciblés pour le harcèlement (exemples publics)
        self.canaux_cibles = ["support_harassment", "anti_bullying_help", "mental_health_support"]
        self.limite_messages = 25  # Messages par canal

        # Initialiser le client Telegram
        self.client = None
        self._initialiser_client()

        logger.info("Telegram Scraper initialisé")
        logger.info(f"Canaux ciblés: {self.canaux_cibles}")

    def _initialiser_client(self):
        """Initialise le client Telegram avec les vraies clés"""
        try:
            if TELETHON_AVAILABLE and self.api_id and self.api_hash:
                self.client = TelegramClient('session_telegram', self.api_id, self.api_hash)
                logger.info("✅ Client Telegram initialisé avec vraies clés API")
                return True
            else:
                logger.warning("⚠️ Telethon non disponible ou clés manquantes - mode simulation")
                return False
        except Exception as e:
            logger.error(f"❌ Erreur initialisation Telegram: {e}")
            self.client = None
            return False
    
    def simuler_donnees_telegram(self, canal, nombre=25):
        """Simule des données Telegram pour la démonstration"""
        logger.info(f"Simulation de données pour canal '{canal}'")
        
        exemples_messages = [
            "Bonjour, je cherche de l'aide car je subis du harcèlement au travail",
            "Comment signaler du cyberharcèlement sur les réseaux sociaux ?",
            "Mon enfant est victime de harcèlement scolaire, que faire ?",
            "Témoignage : j'ai surmonté le harcèlement grâce au soutien",
            "Ressources utiles pour les victimes de harcèlement en ligne",
            "Prévention du harcèlement : sensibilisation dans les écoles",
            "Aide psychologique pour les victimes de harcèlement",
            "Le harcèlement moral au travail : comment réagir ?",
            "Soutien aux parents d'enfants harcelés",
            "Campagne de sensibilisation contre le cyberharcèlement",
            "Témoignage anonyme : sortir du silence face au harcèlement",
            "Outils juridiques contre le harcèlement en ligne",
            "Groupe de parole pour victimes de harcèlement",
            "Comment aider un proche victime de harcèlement ?",
            "Prévention du harcèlement dans les entreprises"
        ]
        
        messages_simules = []
        for i in range(nombre):
            contenu = exemples_messages[i % len(exemples_messages)]
            
            message_simule = {
                "titre": contenu[:100] + "..." if len(contenu) > 100 else contenu,
                "contenu": contenu,
                "auteur": f"user_telegram_{i+1}",
                "date": datetime.now(timezone.utc),
                "url": f"https://t.me/{canal}/{i+1}",
                "source": f"Telegram: {canal}",
                "plateforme": "telegram",
                "post_id": f"tg_{canal}_{i+1}",
                "canal": canal,
                "vues": i * 3,
                "forwards": i % 5,
                "date_collecte": datetime.now(timezone.utc)
            }
            messages_simules.append(message_simule)
        
        return messages_simules
    
    def scraper_canal(self, canal):
        """Scrape un canal Telegram spécifique"""
        logger.info(f"Début scraping Telegram canal: '{canal}'")
        messages_ajoutes = 0
        
        try:
            # Mode simulation (remplacez par l'API Telegram réelle)
            messages_simules = self.simuler_donnees_telegram(canal, self.limite_messages)
            
            for donnees_message in messages_simules:
                # Vérifier si le message simulé existe déjà
                if self.collection.count_documents({"url": donnees_message["url"]}) == 0:
                    self.collection.insert_one(donnees_message)
                    messages_ajoutes += 1
                    logger.info(f"[+] Message ajouté: {donnees_message['titre'][:50]}...")
                else:
                    logger.debug(f"[=] Message existant: {donnees_message['titre'][:50]}...")
                
                time.sleep(0.1)  # Simulation du rate limiting
                
        except Exception as e:
            logger.error(f"Erreur lors du scraping du canal '{canal}': {e}")
        
        logger.info(f"'{canal}': {messages_ajoutes} nouveaux messages")
        return messages_ajoutes
    
    def scraper_tous_canaux(self):
        """Scrape tous les canaux ciblés"""
        logger.info("=" * 60)
        logger.info("DÉBUT COLLECTE TELEGRAM - ÉTAPE 1")
        logger.info("=" * 60)
        
        debut = time.time()
        total_messages = 0
        resultats = {}
        
        for canal in self.canaux_cibles:
            messages_ajoutes = self.scraper_canal(canal)
            resultats[f"Telegram:{canal}"] = messages_ajoutes
            total_messages += messages_ajoutes
            
            # Pause entre canaux
            time.sleep(1)
        
        fin = time.time()
        duree = fin - debut
        
        # Résumé
        logger.info("=" * 60)
        logger.info("RÉSUMÉ COLLECTE TELEGRAM")
        logger.info("=" * 60)
        logger.info(f"Durée totale: {duree:.2f} secondes")
        logger.info(f"Total nouveaux messages: {total_messages}")
        
        for source, count in resultats.items():
            logger.info(f"  {source}: {count} messages")
        
        # Statistiques base de données
        total_db = self.collection.count_documents({"plateforme": "telegram"})
        logger.info(f"Total messages en base: {total_db}")
        
        return resultats
    
    def obtenir_statistiques(self):
        """Obtient les statistiques Telegram de la collection"""
        logger.info("\nSTATISTIQUES TELEGRAM")
        logger.info("-" * 21)
        
        # Total messages
        total_telegram = self.collection.count_documents({"plateforme": "telegram"})
        logger.info(f"Total messages: {total_telegram}")
        
        # Par source Telegram
        pipeline_source = [
            {"$match": {"plateforme": "telegram"}},
            {"$group": {"_id": "$source", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        logger.info("Sources Telegram:")
        for result in self.collection.aggregate(pipeline_source):
            logger.info(f"  {result['_id']}: {result['count']} messages")
        
        return total_telegram
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("Connexions fermées")

def main():
    """Fonction principale"""
    scraper = None
    
    try:
        # Initialiser le scraper
        scraper = TelegramHarcelementScraper()
        
        # Lancer la collecte
        resultats = scraper.scraper_tous_canaux()
        
        # Afficher les statistiques
        scraper.obtenir_statistiques()
        
        logger.info("✅ COLLECTE TELEGRAM TERMINÉE AVEC SUCCÈS")
        
    except KeyboardInterrupt:
        logger.info("Collecte interrompue par l'utilisateur")
    except Exception as e:
        logger.error(f"Erreur: {e}")
    finally:
        if scraper:
            scraper.fermer_connexions()

if __name__ == "__main__":
    main()
