import asyncio
import logging
from datetime import datetime, timezone
import time
from typing import Dict, List, Optional
from config import Config
from database import DatabaseManager

try:
    from telethon import TelegramClient
    from telethon.errors import SessionPasswordNeededError, FloodWaitError
    from telethon.tl.types import Channel, Chat
    TELETHON_AVAILABLE = True
except ImportError:
    TELETHON_AVAILABLE = False
    TelegramClient = None

logger = logging.getLogger(__name__)

class TelegramScraper:
    """Telegram scraper using Telethon library"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager):
        self.config = config
        self.db_manager = db_manager
        self.client = None
        self._session_file = 'telegram_session'
        
        if not TELETHON_AVAILABLE:
            logger.warning("Telethon not available. Install with: pip install telethon")
            return
        
        if not self.config.TELEGRAM_API_ID or not self.config.TELEGRAM_API_HASH:
            logger.warning("Telegram API credentials not provided. Telegram scraping disabled.")
            return
        
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Telegram client"""
        try:
            self.client = TelegramClient(
                self._session_file,
                self.config.TELEGRAM_API_ID,
                self.config.TELEGRAM_API_HASH
            )
            logger.info("Telegram client initialized")
        except Exception as e:
            logger.error(f"Failed to initialize Telegram client: {e}")
            self.client = None
    
    async def connect(self):
        """Connect to Telegram"""
        if not self.client:
            return False
        
        try:
            await self.client.start(phone=self.config.TELEGRAM_PHONE)
            logger.info("Connected to Telegram")
            return True
        except SessionPasswordNeededError:
            logger.error("Two-factor authentication enabled. Please provide password.")
            return False
        except Exception as e:
            logger.error(f"Failed to connect to Telegram: {e}")
            return False
    
    async def scrape_channel(self, channel_username: str, limit: int = 100) -> int:
        """Scrape messages from a Telegram channel"""
        if not self.client:
            logger.warning("Telegram client not initialized")
            return 0
        
        logger.info(f"Scraping Telegram channel: {channel_username}")
        messages_added = 0
        
        try:
            # Get the channel entity
            entity = await self.client.get_entity(channel_username)
            
            # Get messages
            async for message in self.client.iter_messages(entity, limit=limit):
                if not message.text:
                    continue  # Skip messages without text
                
                message_data = self._extract_message_data(message, channel_username, entity)
                
                if self.db_manager.insert_post(message_data):
                    messages_added += 1
                    logger.info(f"Added message: {message.text[:60]}...")
                else:
                    logger.debug(f"Message already exists: {message.id}")
                
                # Rate limiting
                await asyncio.sleep(0.1)
                
        except FloodWaitError as e:
            logger.warning(f"Flood wait error. Waiting {e.seconds} seconds...")
            await asyncio.sleep(e.seconds)
        except Exception as e:
            logger.error(f"Error scraping channel {channel_username}: {str(e)}")
        
        logger.info(f"Finished scraping {channel_username}. Added {messages_added} new messages")
        return messages_added
    
    def _extract_message_data(self, message, channel_username: str, entity) -> Dict:
        """Extract relevant data from a Telegram message"""
        # Get channel title
        channel_title = getattr(entity, 'title', channel_username)
        
        # Create message URL
        message_url = f"https://t.me/{channel_username}/{message.id}"
        
        # Get sender information
        sender_name = "Unknown"
        if message.sender:
            if hasattr(message.sender, 'username') and message.sender.username:
                sender_name = message.sender.username
            elif hasattr(message.sender, 'first_name'):
                sender_name = message.sender.first_name
                if hasattr(message.sender, 'last_name') and message.sender.last_name:
                    sender_name += f" {message.sender.last_name}"
        
        return {
            "title": message.text[:100] + "..." if len(message.text) > 100 else message.text,
            "content": message.text,
            "author": sender_name,
            "date": message.date if message.date else datetime.now(timezone.utc),
            "url": message_url,
            "source": f"Telegram: {channel_title}",
            "platform": "telegram",
            "post_id": str(message.id),
            "channel": channel_username,
            "views": getattr(message, 'views', 0) or 0,
            "forwards": getattr(message, 'forwards', 0) or 0
        }
    
    async def search_messages(self, channel_username: str, query: str, limit: int = 100) -> int:
        """Search for specific messages in a channel"""
        if not self.client:
            logger.warning("Telegram client not initialized")
            return 0
        
        logger.info(f"Searching in {channel_username} for: {query}")
        messages_added = 0
        
        try:
            entity = await self.client.get_entity(channel_username)
            
            async for message in self.client.iter_messages(entity, search=query, limit=limit):
                if not message.text:
                    continue
                
                message_data = self._extract_message_data(message, channel_username, entity)
                
                if self.db_manager.insert_post(message_data):
                    messages_added += 1
                    logger.info(f"Added message: {message.text[:60]}...")
                
                await asyncio.sleep(0.1)
                
        except Exception as e:
            logger.error(f"Error searching in {channel_username}: {str(e)}")
        
        logger.info(f"Finished searching {channel_username}. Added {messages_added} new messages")
        return messages_added
    
    async def scrape_all_channels(self) -> Dict[str, int]:
        """Scrape all configured channels"""
        results = {}
        
        if not self.client or not self.config.TELEGRAM_CHANNELS:
            logger.warning("No Telegram channels configured or client not available")
            return results
        
        if not await self.connect():
            logger.error("Failed to connect to Telegram")
            return results
        
        try:
            for channel in self.config.TELEGRAM_CHANNELS:
                messages_added = await self.scrape_channel(channel, self.config.TELEGRAM_LIMIT_MESSAGES)
                results[channel] = messages_added
                
                # Rate limiting between channels
                await asyncio.sleep(self.config.RATE_LIMIT_DELAY)
                
        finally:
            await self.disconnect()
        
        return results
    
    async def search_harassment_keywords(self) -> Dict[str, int]:
        """Search for harassment-related keywords in configured channels"""
        results = {}
        
        if not self.client or not self.config.TELEGRAM_CHANNELS:
            logger.warning("No Telegram channels configured or client not available")
            return results
        
        if not await self.connect():
            logger.error("Failed to connect to Telegram")
            return results
        
        try:
            for channel in self.config.TELEGRAM_CHANNELS:
                for keyword in self.config.HARASSMENT_KEYWORDS:
                    search_key = f"{channel}:{keyword}"
                    messages_added = await self.search_messages(channel, keyword, 50)
                    results[search_key] = messages_added
                    
                    await asyncio.sleep(1)  # Rate limiting
                    
        finally:
            await self.disconnect()
        
        return results
    
    async def get_channel_info(self, channel_username: str) -> Optional[Dict]:
        """Get information about a Telegram channel"""
        if not self.client:
            return None
        
        try:
            entity = await self.client.get_entity(channel_username)
            
            return {
                "id": entity.id,
                "title": getattr(entity, 'title', 'Unknown'),
                "username": getattr(entity, 'username', channel_username),
                "participants_count": getattr(entity, 'participants_count', 0),
                "description": getattr(entity, 'about', ''),
                "type": "channel" if isinstance(entity, Channel) else "chat"
            }
        except Exception as e:
            logger.error(f"Error getting channel info for {channel_username}: {e}")
            return None
    
    async def disconnect(self):
        """Disconnect from Telegram"""
        if self.client:
            await self.client.disconnect()
            logger.info("Disconnected from Telegram")
    
    def is_available(self) -> bool:
        """Check if Telegram scraper is available"""
        return TELETHON_AVAILABLE and self.client is not None
    
    def run_scraping(self) -> Dict[str, int]:
        """Run scraping in synchronous context"""
        if not self.is_available():
            logger.warning("Telegram scraper not available")
            return {}
        
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        try:
            return loop.run_until_complete(self.scrape_all_channels())
        finally:
            if not loop.is_running():
                loop.close()
