# 📤 Exports de Données - Analyse du Harcèlement en Ligne

## 🎯 Vue d'ensemble

Ce dossier contient tous les exports des données d'analyse du harcèlement en ligne, générés depuis MongoDB et Elasticsearch dans différents formats pour faciliter l'utilisation, l'analyse et le partage.

## 📊 Résumé des données

- **📄 Total documents** : 221 documents de harcèlement analysés
- **🌍 Langues** : 4 langues détectées (anglais 65%, français 34%, autres 1%)
- **😊 Sentiments** : 39% négatif, 35% neutre, 25% positif
- **📱 Plateformes** : Reddit (66%), Telegram (34%)
- **📈 Scores** : -0.999 à +0.998 (moyenne: -0.139)

## 📁 Structure des fichiers

### 📊 Exports MongoDB (12 fichiers)

#### **JSON Complet** (674 KB chacun)
- `mongodb_export_complet_*.json` : Données complètes avec métadonnées
- **Usage** : Sauvegarde complète, intégration système
- **Contenu** : Tous les champs, données brutes et analysées

#### **JSON Analyses** (503 KB chacun)
- `mongodb_export_analyses_*.json` : Données analysées uniquement
- **Usage** : Analyse, visualisation, API
- **Contenu** : Champs optimisés pour l'analyse

#### **CSV** (346 KB chacun)
- `mongodb_export_*.csv` : Format tabulaire
- **Usage** : Excel, LibreOffice, analyse statistique
- **Contenu** : 14 colonnes principales

#### **Excel** (31 KB chacun)
- `mongodb_export_*.xlsx` : Fichier Excel multi-feuilles
- **Usage** : Présentation, analyse business
- **Contenu** : 4 feuilles (données, stats plateformes, langues, top négatifs)

#### **Échantillons** (140-149 KB chacun)
- `mongodb_echantillon_50_*.json` : 50 documents représentatifs
- **Usage** : Tests, démonstration, développement
- **Contenu** : Échantillonnage stratifié par sentiment

#### **Statistiques** (1.6 KB chacun)
- `mongodb_statistiques_*.json` : Métriques détaillées
- **Usage** : Rapports, monitoring, KPI
- **Contenu** : Agrégations, évolution temporelle

### 🔍 Exports Elasticsearch (12 fichiers)

#### **JSON Elasticsearch** (399 KB chacun)
- `elasticsearch_export_*.json` : Format Elasticsearch
- **Usage** : Backup ES, migration, analyse
- **Contenu** : Documents avec métadonnées ES

#### **NDJSON Bulk** (338 KB chacun)
- `elasticsearch_bulk_*.ndjson` : Format import bulk
- **Usage** : Import direct dans Elasticsearch
- **Commande** : `curl -X POST 'localhost:9200/_bulk' -H 'Content-Type: application/json' --data-binary @fichier.ndjson`

#### **CSV Elasticsearch** (63 KB chacun)
- `elasticsearch_export_*.csv` : Format tabulaire ES
- **Usage** : Analyse comparative, validation
- **Contenu** : Champs ES optimisés

#### **Agrégations** (1.7 KB chacun)
- `elasticsearch_agregations_*.json` : Statistiques ES
- **Usage** : Dashboards, métriques, KPI
- **Contenu** : Répartitions par sentiment, langue, plateforme

#### **Mapping** (2 KB chacun)
- `elasticsearch_mapping_*.json` : Configuration index
- **Usage** : Création index, optimisation
- **Contenu** : Types de champs, analyseurs

#### **Requêtes** (3 KB chacun)
- `elasticsearch_requetes_*.json` : Exemples de requêtes
- **Usage** : Développement, formation, documentation
- **Contenu** : 8 requêtes types avec explications

### 📋 Rapports (1 fichier)

#### **Rapport d'export** (3.6 KB)
- `rapport_export_*.json` : Métadonnées d'export
- **Usage** : Audit, traçabilité, monitoring
- **Contenu** : Statistiques d'export, recommandations

## 🚀 Guide d'utilisation

### 📊 Analyse rapide

```bash
# Ouvrir dans Excel
open mongodb_export_*.xlsx

# Analyse Python
import pandas as pd
df = pd.read_csv('mongodb_export_*.csv')
df.describe()
```

### 🔧 Intégration système

```python
# Charger JSON en Python
import json
with open('mongodb_export_analyses_*.json', 'r') as f:
    data = json.load(f)
documents = data['documents']

# API REST
curl -X GET "localhost:9200/harcelement_posts/_search" \
     -H 'Content-Type: application/json' \
     -d @elasticsearch_requetes_*.json
```

### 🔍 Import Elasticsearch

```bash
# Créer l'index avec mapping
curl -X PUT "localhost:9200/harcelement_posts" \
     -H 'Content-Type: application/json' \
     -d @elasticsearch_mapping_*.json

# Importer les données
curl -X POST "localhost:9200/_bulk" \
     -H 'Content-Type: application/json' \
     --data-binary @elasticsearch_bulk_*.ndjson
```

### 📈 Visualisation

```python
# Tableau/PowerBI : Importer CSV
# Kibana : Utiliser les agrégations
# Python/Matplotlib
import matplotlib.pyplot as plt
sentiments = df['sentiment'].value_counts()
sentiments.plot(kind='pie')
plt.show()
```

## 📊 Contenu détaillé des données

### 😊 Répartition des sentiments
- **Négatif** : 87 documents (39.4%) - Harcèlement, détresse
- **Neutre** : 78 documents (35.3%) - Informatif, questions
- **Positif** : 56 documents (25.3%) - Support, encouragement

### 🌍 Répartition des langues
- **Anglais** : 144 documents (65.2%) - Langue dominante
- **Français** : 75 documents (33.9%) - Seconde langue
- **Espagnol** : 1 document (0.5%)
- **Vietnamien** : 1 document (0.5%)

### 📱 Répartition des plateformes
- **Reddit** : 146 documents (66.1%) - r/bullying, r/cyberbullying, r/TrueOffMyChest
- **Telegram** : 75 documents (33.9%) - Canaux de support

### 📈 Distribution des scores
- **Très négatif** (≤ -0.5) : 76 documents (34.4%)
- **Négatif** (-0.5 à -0.1) : 10 documents (4.5%)
- **Neutre** (-0.1 à 0.1) : 79 documents (35.7%)
- **Positif** (0.1 à 0.5) : 13 documents (5.9%)
- **Très positif** (≥ 0.5) : 43 documents (19.5%)

## 🔧 Formats de fichiers

### JSON
- **Avantages** : Structure complète, métadonnées, APIs
- **Usage** : Intégration, sauvegarde, développement
- **Outils** : Python, JavaScript, APIs REST

### CSV
- **Avantages** : Universel, Excel, analyse statistique
- **Usage** : Analyse rapide, présentation, import
- **Outils** : Excel, R, Python pandas, Tableau

### NDJSON
- **Avantages** : Import bulk Elasticsearch
- **Usage** : Production Elasticsearch, migration
- **Outils** : curl, Elasticsearch bulk API

### Excel
- **Avantages** : Multi-feuilles, graphiques, business
- **Usage** : Rapports, présentation, analyse métier
- **Outils** : Excel, LibreOffice, Google Sheets

## ⚡ Commandes utiles

### Validation des données
```bash
# Compter les lignes CSV
wc -l mongodb_export_*.csv

# Vérifier JSON
python -m json.tool mongodb_export_analyses_*.json > /dev/null

# Taille des fichiers
ls -lh *.json *.csv *.xlsx
```

### Analyse rapide
```bash
# Top 10 auteurs les plus actifs
cut -d',' -f3 mongodb_export_*.csv | sort | uniq -c | sort -nr | head -10

# Répartition par plateforme
cut -d',' -f5 mongodb_export_*.csv | sort | uniq -c
```

## 🎯 Cas d'usage

### 🔬 Recherche académique
- **Fichiers** : JSON analyses, statistiques
- **Outils** : Python, R, SPSS
- **Métriques** : Sentiment, langue, évolution temporelle

### 💼 Analyse business
- **Fichiers** : Excel, CSV
- **Outils** : Excel, Tableau, PowerBI
- **Visualisations** : Graphiques, tableaux de bord

### 🔧 Développement
- **Fichiers** : JSON, NDJSON, mapping
- **Outils** : APIs, Elasticsearch, Kibana
- **Intégration** : Systèmes de modération, alertes

### 🎓 Formation
- **Fichiers** : Échantillons, requêtes exemples
- **Outils** : Jupyter, documentation
- **Apprentissage** : NLP, analyse de sentiment

## 📋 Métadonnées

- **Date d'export** : 7 décembre 2024
- **Version** : 1.0.0
- **Source** : MongoDB harcelement.posts
- **Pipeline** : Collecte → Prétraitement → NLP → Export
- **Qualité** : 100% de réussite, aucune perte de données

## 🔒 Considérations éthiques

- **Anonymisation** : Aucune donnée personnelle identifiable
- **Usage** : Recherche et prévention du harcèlement uniquement
- **Conformité** : RGPD, conditions d'utilisation des plateformes
- **Responsabilité** : Usage éthique et responsable requis

## 📞 Support

Pour toute question sur les exports :
- **Documentation** : README.md principal
- **Scripts** : `apercu_exports.py`, `rapport_exports_final.py`
- **Validation** : `python -m json.tool fichier.json`

---

**📊 25 fichiers générés • 5.0 MB • 221 documents analysés • Prêt pour utilisation**
