# Social Media Harassment Data Collector

A comprehensive Python-based data collection system for gathering harassment-related content from social media platforms (Reddit, Twitter, Telegram) and storing it in MongoDB for analysis.

## 🎯 Project Overview

This project implements a technical test for Data Engineer position, focusing on:
- **Python** (scraping, preprocessing, NLP)
- **MongoDB** (NoSQL storage)
- **Elasticsearch & Kibana** (indexation, visualization)
- **ELK Stack** (installation, security, customization)

## 📋 Features

- **Multi-platform scraping**: Reddit, Twitter/X, Telegram
- **MongoDB storage**: Structured data storage with indexing
- **Duplicate detection**: Prevents data duplication
- **Rate limiting**: Respects API limits
- **Comprehensive logging**: Detailed operation logs
- **Configuration management**: Environment-based configuration
- **Error handling**: Robust error recovery

## 🚀 Quick Start

### 1. Prerequisites

- Python 3.8+
- MongoDB (local or remote)
- API credentials for platforms you want to scrape

### 2. Installation

```bash
# Clone the repository
git clone <repository-url>
cd harassment-data-collector

# Install dependencies
pip install -r requirements.txt
```

### 3. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your API credentials
nano .env
```

### 4. API Setup

#### Reddit API
1. Go to https://www.reddit.com/prefs/apps
2. Create a new application
3. Copy client_id and client_secret to .env

#### Twitter API (Optional)
1. Go to https://developer.twitter.com/en/portal/dashboard
2. Create a new project/app
3. Copy Bearer Token and API keys to .env

#### Telegram API (Optional)
1. Go to https://my.telegram.org/apps
2. Create a new application
3. Copy API ID and Hash to .env

### 5. Run Collection

```bash
# Run all platforms
python main.py

# Run individual scrapers
python scraper.py          # Reddit only
python twitter_scraper.py  # Twitter only (if configured)
python telegram_scraper.py # Telegram only (if configured)
```

## 📁 Project Structure

```
harassment-data-collector/
├── main.py                 # Main orchestrator
├── config.py              # Configuration management
├── database.py            # MongoDB utilities
├── scraper.py             # Reddit scraper (enhanced)
├── twitter_scraper.py     # Twitter scraper
├── telegram_scraper.py    # Telegram scraper
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
├── README.md             # This file
└── logs/                 # Log files (created automatically)
```

## 🗃️ Data Schema

Each collected post contains:

```json
{
  "title": "Post title or excerpt",
  "content": "Full post content",
  "author": "Username or display name",
  "date": "2024-01-01T12:00:00Z",
  "url": "https://platform.com/post/url",
  "source": "r/subreddit or Twitter search: keyword",
  "platform": "reddit|twitter|telegram",
  "post_id": "unique_platform_id",
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `MONGODB_URI` | MongoDB connection string | `mongodb://localhost:27017/` |
| `MONGODB_DATABASE` | Database name | `harcelement` |
| `MONGODB_COLLECTION` | Collection name | `posts` |
| `REDDIT_SUBREDDITS` | Comma-separated subreddits | `bullying,TrueOffMyChest` |
| `TWITTER_KEYWORDS` | Comma-separated keywords | `harassment,bullying` |
| `RATE_LIMIT_DELAY` | Delay between requests (seconds) | `2.0` |

## 📊 Database Operations

### View Statistics
```python
from database import DatabaseManager
from config import Config

config = Config()
db = DatabaseManager(config)
stats = db.get_statistics()
print(stats)
```

### Search Posts
```python
# Search by text
results = db.search_posts("cyberbullying")

# Get recent posts
recent = db.get_recent_posts(days=7)

# Get posts by platform
reddit_posts = db.get_posts_by_platform("reddit")
```

## 🔍 Monitoring and Logs

- **Application logs**: `harassment_scraper.log`
- **Individual scraper logs**: `scraper.log`
- **Console output**: Real-time progress updates

## ⚠️ Important Notes

### Legal and Ethical Considerations
- Only collect public data
- Respect platform Terms of Service
- Implement appropriate rate limiting
- Consider data privacy regulations

### Rate Limiting
- Reddit: 60 requests per minute
- Twitter: Varies by endpoint
- Telegram: Built-in flood protection

### Error Handling
- Automatic retry on temporary failures
- Graceful degradation when APIs unavailable
- Comprehensive error logging

## 🛠️ Development

### Running Tests
```bash
pip install pytest pytest-asyncio
pytest tests/
```

### Code Formatting
```bash
pip install black flake8
black .
flake8 .
```

## 📈 Next Steps (ELK Stack Integration)

1. **Elasticsearch Setup**
   - Install Elasticsearch
   - Create index mappings
   - Implement data pipeline from MongoDB

2. **Kibana Visualization**
   - Set up dashboards
   - Create visualizations
   - Configure alerts

3. **Logstash Processing**
   - Data transformation
   - Enrichment pipelines
   - Real-time processing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is for educational and research purposes. Please ensure compliance with platform APIs and local regulations.

## 🆘 Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Ensure MongoDB is running
   - Check connection string in .env

2. **API Authentication Failed**
   - Verify API credentials
   - Check credential format

3. **Rate Limit Exceeded**
   - Increase RATE_LIMIT_DELAY
   - Reduce collection limits

4. **Import Errors**
   - Install missing dependencies
   - Check Python version compatibility

### Getting Help

- Check logs for detailed error messages
- Verify API credentials and permissions
- Ensure all dependencies are installed
- Check platform API status pages
