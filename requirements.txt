# Analyse du Harcèlement en Ligne - Dépendances Python
# Version: 1.0.0
# Dernière mise à jour: Décembre 2024

# ===== BASES DE DONNÉES =====
pymongo>=4.0.0                 # MongoDB driver
elasticsearch>=8.0.0           # Elasticsearch client

# ===== COLLECTE DE DONNÉES =====
praw>=7.6.0                    # Reddit API wrapper
python-telegram-bot>=20.0     # Telegram Bot API
tweepy>=4.14.0                 # Twitter API v2
telethon>=1.28.0               # Telegram client library

# ===== TRAITEMENT NLP =====
nltk>=3.8                      # Natural Language Toolkit
textblob>=0.17.1               # Sentiment analysis
vaderSentiment>=3.3.2          # Social media sentiment analysis
langdetect>=1.0.9              # Language detection

# ===== VISUALISATION =====
matplotlib>=3.6.0              # Plotting library
seaborn>=0.12.0                # Statistical visualization
pandas>=1.5.0                  # Data manipulation
numpy>=1.24.0                  # Numerical computing

# ===== UTILITAIRES =====
requests>=2.28.0               # HTTP library
python-dateutil>=2.8.2        # Date utilities
pytz>=2022.7                   # Timezone handling
tqdm>=4.64.0                   # Progress bars

# ===== DÉVELOPPEMENT (optionnel) =====
pytest>=7.2.0                 # Testing framework
pytest-cov>=4.0.0             # Coverage plugin
black>=22.10.0                 # Code formatter
flake8>=6.0.0                  # Linting
sphinx>=5.3.0                 # Documentation generator

# ===== SÉCURITÉ =====
cryptography>=38.0.0           # Cryptographic recipes
python-dotenv>=0.21.0          # Environment variables

# ===== MONITORING (optionnel) =====
prometheus-client>=0.15.0      # Metrics collection
psutil>=5.9.0                  # System monitoring

# ===== DÉPLOIEMENT (optionnel) =====
gunicorn>=20.1.0               # WSGI HTTP Server
docker>=6.0.0                  # Docker SDK

# ===== NOTES D'INSTALLATION =====
# 
# Installation standard:
# pip install -r requirements.txt
#
# Installation développement:
# pip install -r requirements.txt -r requirements-dev.txt
#
# Installation production:
# pip install -r requirements.txt --no-dev
#
# Mise à jour:
# pip install -r requirements.txt --upgrade
#
# Vérification:
# pip check
#
# ===== COMPATIBILITÉ =====
# Python: 3.8+
# OS: Linux, macOS, Windows
# Architecture: x86_64, ARM64
#
# ===== RESSOURCES NLTK REQUISES =====
# Télécharger après installation:
# python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('vader_lexicon')"
