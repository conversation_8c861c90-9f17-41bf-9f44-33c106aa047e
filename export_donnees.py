#!/usr/bin/env python3
"""
Export complet des données
Orchestre l'export depuis MongoDB et Elasticsearch vers tous les formats
"""

import sys
import time
import logging
import argparse
from datetime import datetime
from pathlib import Path

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('exports.log')
    ]
)
logger = logging.getLogger(__name__)

class ExportOrchestrator:
    """Orchestrateur des exports de données"""
    
    def __init__(self):
        """Initialise l'orchestrateur"""
        self.start_time = None
        self.exports_realises = []
        self.exports_echoues = []
        
        # Créer le dossier d'export principal
        self.export_dir = Path("exports")
        self.export_dir.mkdir(exist_ok=True)
        
        logger.info("🚀 Orchestrateur d'export initialisé")
        logger.info(f"📁 Dossier d'export: {self.export_dir}")
    
    def verifier_donnees_disponibles(self):
        """Vérifie que les données sont disponibles pour export"""
        logger.info("🔍 Vérification des données disponibles...")
        
        try:
            from pymongo import MongoClient
            
            # Vérifier MongoDB
            client = MongoClient("mongodb://localhost:27017/", serverSelectionTimeoutMS=5000)
            db = client["harcelement"]
            collection = db["posts"]
            
            total_docs = collection.count_documents({})
            analyzed_docs = collection.count_documents({"nlp_effectue": True})
            
            if total_docs == 0:
                logger.error("❌ Aucune donnée trouvée dans MongoDB")
                return False
            
            if analyzed_docs == 0:
                logger.warning("⚠️ Aucune donnée analysée trouvée")
                logger.info("💡 Exécutez d'abord le pipeline d'analyse (étapes 1-3)")
            
            logger.info(f"✅ MongoDB: {total_docs} documents total, {analyzed_docs} analysés")
            client.close()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur vérification données: {e}")
            return False
    
    def export_mongodb(self):
        """Exporte les données MongoDB"""
        logger.info("\n" + "="*60)
        logger.info("📊 EXPORT MONGODB")
        logger.info("="*60)
        
        try:
            from export_mongodb import MongoDBExporter
            
            exporter = MongoDBExporter()
            success = exporter.export_tout()
            exporter.fermer_connexions()
            
            if success:
                self.exports_realises.append("MongoDB")
                logger.info("✅ Export MongoDB réussi")
                return True
            else:
                self.exports_echoues.append(("MongoDB", "Erreur interne"))
                logger.error("❌ Export MongoDB échoué")
                return False
                
        except Exception as e:
            self.exports_echoues.append(("MongoDB", str(e)))
            logger.error(f"❌ Erreur export MongoDB: {e}")
            return False
    
    def export_elasticsearch(self):
        """Exporte les données Elasticsearch (simulation)"""
        logger.info("\n" + "="*60)
        logger.info("🔍 EXPORT ELASTICSEARCH (SIMULATION)")
        logger.info("="*60)
        
        try:
            from export_elasticsearch import ElasticsearchExporter
            
            exporter = ElasticsearchExporter()
            success = exporter.export_tout()
            exporter.fermer_connexions()
            
            if success:
                self.exports_realises.append("Elasticsearch")
                logger.info("✅ Export Elasticsearch réussi")
                return True
            else:
                self.exports_echoues.append(("Elasticsearch", "Erreur interne"))
                logger.error("❌ Export Elasticsearch échoué")
                return False
                
        except Exception as e:
            self.exports_echoues.append(("Elasticsearch", str(e)))
            logger.error(f"❌ Erreur export Elasticsearch: {e}")
            return False
    
    def generer_rapport_export(self):
        """Génère un rapport détaillé des exports"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        rapport_file = self.export_dir / f"rapport_export_{timestamp}.json"
        
        logger.info(f"📋 Génération rapport d'export: {rapport_file}")
        
        # Lister tous les fichiers d'export
        fichiers_export = []
        for fichier in self.export_dir.glob("*"):
            if fichier.is_file() and fichier.name != rapport_file.name:
                fichiers_export.append({
                    "nom": fichier.name,
                    "taille_kb": round(fichier.stat().st_size / 1024, 2),
                    "date_creation": datetime.fromtimestamp(fichier.stat().st_mtime).isoformat(),
                    "extension": fichier.suffix
                })
        
        # Statistiques par type de fichier
        stats_extensions = {}
        for fichier in fichiers_export:
            ext = fichier["extension"]
            if ext not in stats_extensions:
                stats_extensions[ext] = {"count": 0, "taille_totale_kb": 0}
            stats_extensions[ext]["count"] += 1
            stats_extensions[ext]["taille_totale_kb"] += fichier["taille_kb"]
        
        # Rapport complet
        rapport = {
            "metadata": {
                "date_rapport": datetime.now().isoformat(),
                "duree_export_secondes": time.time() - self.start_time if self.start_time else 0,
                "version": "1.0.0"
            },
            "resultats": {
                "exports_realises": self.exports_realises,
                "exports_echoues": [{"source": source, "erreur": erreur} for source, erreur in self.exports_echoues],
                "taux_reussite": len(self.exports_realises) / (len(self.exports_realises) + len(self.exports_echoues)) * 100 if (self.exports_realises or self.exports_echoues) else 0
            },
            "fichiers": {
                "total_fichiers": len(fichiers_export),
                "taille_totale_kb": sum(f["taille_kb"] for f in fichiers_export),
                "statistiques_extensions": stats_extensions,
                "liste_fichiers": fichiers_export
            },
            "recommandations": {
                "formats_recommandes": {
                    "analyse_rapide": "CSV (facile à ouvrir dans Excel)",
                    "integration_systeme": "JSON (format standard)",
                    "import_elasticsearch": "NDJSON (format bulk)",
                    "presentation": "Excel (graphiques intégrés)"
                },
                "utilisation": {
                    "recherche": "Utiliser les fichiers JSON avec requêtes exemples",
                    "visualisation": "Importer CSV dans Tableau/PowerBI",
                    "backup": "Conserver JSON complet",
                    "partage": "Utiliser échantillons pour démonstration"
                }
            }
        }
        
        # Sauvegarder le rapport
        import json
        with open(rapport_file, 'w', encoding='utf-8') as f:
            json.dump(rapport, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Rapport d'export généré: {rapport_file}")
        return rapport
    
    def afficher_resume_final(self, rapport):
        """Affiche le résumé final des exports"""
        logger.info("\n" + "="*70)
        logger.info("📊 RÉSUMÉ FINAL DES EXPORTS")
        logger.info("="*70)
        
        # Résultats généraux
        total_exports = len(self.exports_realises) + len(self.exports_echoues)
        duree = rapport["metadata"]["duree_export_secondes"]
        
        logger.info(f"⏱️ Durée totale: {duree:.1f} secondes ({duree/60:.1f} minutes)")
        logger.info(f"📈 Taux de réussite: {rapport['resultats']['taux_reussite']:.1f}%")
        
        # Exports réussis
        if self.exports_realises:
            logger.info(f"\n✅ EXPORTS RÉUSSIS ({len(self.exports_realises)}):")
            for source in self.exports_realises:
                logger.info(f"   ✓ {source}")
        
        # Exports échoués
        if self.exports_echoues:
            logger.info(f"\n❌ EXPORTS ÉCHOUÉS ({len(self.exports_echoues)}):")
            for source, erreur in self.exports_echoues:
                logger.info(f"   ✗ {source}: {erreur}")
        
        # Statistiques fichiers
        fichiers = rapport["fichiers"]
        logger.info(f"\n📁 FICHIERS GÉNÉRÉS:")
        logger.info(f"   Total: {fichiers['total_fichiers']} fichiers")
        logger.info(f"   Taille: {fichiers['taille_totale_kb']:.1f} KB ({fichiers['taille_totale_kb']/1024:.1f} MB)")
        
        # Par extension
        logger.info(f"\n📋 RÉPARTITION PAR FORMAT:")
        for ext, stats in fichiers["statistiques_extensions"].items():
            logger.info(f"   {ext or 'sans extension'}: {stats['count']} fichiers ({stats['taille_totale_kb']:.1f} KB)")
        
        # Recommandations
        logger.info(f"\n💡 RECOMMANDATIONS D'UTILISATION:")
        logger.info(f"   📊 Analyse rapide: Fichiers CSV")
        logger.info(f"   🔧 Intégration: Fichiers JSON")
        logger.info(f"   🔍 Import Elasticsearch: Fichiers NDJSON")
        logger.info(f"   📈 Présentation: Fichiers Excel")
        
        logger.info(f"\n📁 Tous les fichiers dans: {self.export_dir}")
        
        # Résultat final
        if len(self.exports_echoues) == 0:
            logger.info("\n🎉 TOUS LES EXPORTS TERMINÉS AVEC SUCCÈS !")
            logger.info("✅ Données prêtes pour analyse et partage")
            return True
        else:
            logger.warning(f"\n⚠️ EXPORTS TERMINÉS AVEC {len(self.exports_echoues)} ERREUR(S)")
            logger.info("💡 Consultez les logs pour résoudre les problèmes")
            return False
    
    def export_complet(self, sources=None):
        """Exporte toutes les données depuis toutes les sources"""
        self.start_time = time.time()
        sources = sources or ["mongodb", "elasticsearch"]
        
        logger.info("🚀 DÉBUT EXPORT COMPLET DES DONNÉES")
        logger.info(f"🎯 Sources: {', '.join(sources)}")
        logger.info(f"🕐 Heure de début: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Vérifier les données
        if not self.verifier_donnees_disponibles():
            logger.error("❌ Données non disponibles pour export")
            return False
        
        # Exporter selon les sources demandées
        if "mongodb" in sources:
            self.export_mongodb()
        
        if "elasticsearch" in sources:
            self.export_elasticsearch()
        
        # Générer le rapport
        rapport = self.generer_rapport_export()
        
        # Afficher le résumé
        success = self.afficher_resume_final(rapport)
        
        return success

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(
        description="Export complet des données d'analyse du harcèlement",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemples d'utilisation:
  python export_donnees.py                    # Export complet (MongoDB + Elasticsearch)
  python export_donnees.py --source mongodb   # MongoDB uniquement
  python export_donnees.py --source elasticsearch  # Elasticsearch uniquement
  python export_donnees.py --verbose          # Logs détaillés
        """
    )
    
    parser.add_argument(
        '--source',
        choices=['mongodb', 'elasticsearch', 'all'],
        default='all',
        help='Source de données à exporter'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Logs détaillés'
    )
    
    args = parser.parse_args()
    
    # Configuration logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Initialiser l'orchestrateur
        orchestrator = ExportOrchestrator()
        
        # Déterminer les sources
        if args.source == 'all':
            sources = ['mongodb', 'elasticsearch']
        else:
            sources = [args.source]
        
        # Exporter
        success = orchestrator.export_complet(sources=sources)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ Export interrompu par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
