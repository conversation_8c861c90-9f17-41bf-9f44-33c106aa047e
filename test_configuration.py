#!/usr/bin/env python3
"""
Script de test de configuration
Valide que toutes les dépendances et configurations sont correctes
"""

import sys
import logging
from datetime import datetime

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_python_version():
    """Teste la version Python"""
    logger.info("🐍 Test version Python...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error(f"❌ Python 3.8+ requis, trouvé: {version.major}.{version.minor}")
        return False
    
    logger.info(f"✅ Python {version.major}.{version.minor}.{version.micro} OK")
    return True

def test_dependencies():
    """Teste l'installation des dépendances"""
    logger.info("📦 Test des dépendances...")
    
    dependencies = [
        ("pymongo", "MongoDB driver"),
        ("praw", "Reddit API"),
        ("nltk", "Natural Language Toolkit"),
        ("textblob", "Sentiment analysis"),
        ("vaderSentiment", "Social media sentiment"),
        ("langdetect", "Language detection"),
        ("matplotlib", "Plotting"),
        ("pandas", "Data manipulation"),
        ("requests", "HTTP client")
    ]
    
    missing = []
    for package, description in dependencies:
        try:
            __import__(package)
            logger.info(f"✅ {package} ({description}) OK")
        except ImportError:
            logger.error(f"❌ {package} ({description}) MANQUANT")
            missing.append(package)
    
    if missing:
        logger.error(f"❌ Dépendances manquantes: {', '.join(missing)}")
        logger.info("💡 Installez avec: pip install -r requirements.txt")
        return False
    
    logger.info("✅ Toutes les dépendances sont installées")
    return True

def test_nltk_data():
    """Teste les données NLTK"""
    logger.info("📚 Test des données NLTK...")
    
    try:
        import nltk
        
        # Tester les ressources requises
        resources = [
            ('punkt', 'Tokenizer'),
            ('stopwords', 'Stop words'),
            ('vader_lexicon', 'VADER lexicon')
        ]
        
        missing = []
        for resource, description in resources:
            try:
                nltk.data.find(f'tokenizers/{resource}' if resource == 'punkt' 
                              else f'corpora/{resource}' if resource == 'stopwords'
                              else f'vader_lexicon/{resource}')
                logger.info(f"✅ NLTK {resource} ({description}) OK")
            except LookupError:
                logger.warning(f"⚠️ NLTK {resource} ({description}) MANQUANT")
                missing.append(resource)
        
        if missing:
            logger.info("💡 Téléchargement des ressources manquantes...")
            for resource in missing:
                try:
                    nltk.download(resource, quiet=True)
                    logger.info(f"✅ NLTK {resource} téléchargé")
                except Exception as e:
                    logger.error(f"❌ Erreur téléchargement {resource}: {e}")
                    return False
        
        logger.info("✅ Toutes les données NLTK sont disponibles")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur NLTK: {e}")
        return False

def test_mongodb_connection():
    """Teste la connexion MongoDB"""
    logger.info("🍃 Test connexion MongoDB...")
    
    try:
        from pymongo import MongoClient
        
        # Tentative de connexion
        client = MongoClient("mongodb://localhost:27017/", serverSelectionTimeoutMS=5000)
        
        # Test de ping
        client.admin.command('ping')
        
        # Test base de données
        db = client["harcelement"]
        collection = db["posts"]
        
        # Test d'écriture/lecture
        test_doc = {"test": True, "timestamp": datetime.now()}
        result = collection.insert_one(test_doc)
        
        # Vérifier insertion
        found = collection.find_one({"_id": result.inserted_id})
        if found:
            collection.delete_one({"_id": result.inserted_id})
            logger.info("✅ MongoDB connexion et opérations OK")
            client.close()
            return True
        else:
            logger.error("❌ Erreur test lecture MongoDB")
            client.close()
            return False
            
    except Exception as e:
        logger.error(f"❌ Erreur MongoDB: {e}")
        logger.info("💡 Vérifiez que MongoDB est démarré: mongod --dbpath /data/db")
        return False

def test_elasticsearch_connection():
    """Teste la connexion Elasticsearch (optionnel)"""
    logger.info("🔍 Test connexion Elasticsearch...")
    
    try:
        from elasticsearch import Elasticsearch
        
        # Tentative de connexion
        es = Elasticsearch([{"host": "localhost", "port": 9200, "scheme": "http"}])
        
        # Test de ping
        if es.ping():
            logger.info("✅ Elasticsearch connexion OK")
            return True
        else:
            logger.warning("⚠️ Elasticsearch non accessible (optionnel)")
            return True  # Non critique
            
    except ImportError:
        logger.warning("⚠️ Elasticsearch client non installé (optionnel)")
        return True  # Non critique
    except Exception as e:
        logger.warning(f"⚠️ Elasticsearch non accessible: {e} (optionnel)")
        return True  # Non critique

def test_configuration_file():
    """Teste le fichier de configuration"""
    logger.info("⚙️ Test fichier de configuration...")
    
    try:
        # Vérifier existence config.py
        try:
            import config
            logger.info("✅ Fichier config.py trouvé")
        except ImportError:
            logger.warning("⚠️ Fichier config.py non trouvé")
            logger.info("💡 Copiez config.example.py vers config.py et configurez vos clés API")
            return True  # Non critique pour les tests
        
        # Valider configuration si disponible
        if hasattr(config, 'validate_config'):
            try:
                config.validate_config()
                logger.info("✅ Configuration valide")
            except ValueError as e:
                logger.warning(f"⚠️ Configuration incomplète: {e}")
                logger.info("💡 Éditez config.py avec vos vraies clés API")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur configuration: {e}")
        return False

def test_sentiment_analysis():
    """Teste l'analyse de sentiment"""
    logger.info("😊 Test analyse de sentiment...")
    
    try:
        from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
        from textblob import TextBlob
        
        # Test VADER
        analyzer = SentimentIntensityAnalyzer()
        vader_result = analyzer.polarity_scores("I am happy")
        
        if 'compound' in vader_result:
            logger.info("✅ VADER sentiment analysis OK")
        else:
            logger.error("❌ VADER résultat invalide")
            return False
        
        # Test TextBlob
        blob = TextBlob("I am sad")
        textblob_result = blob.sentiment.polarity
        
        if isinstance(textblob_result, (int, float)):
            logger.info("✅ TextBlob sentiment analysis OK")
        else:
            logger.error("❌ TextBlob résultat invalide")
            return False
        
        logger.info("✅ Analyse de sentiment fonctionnelle")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur analyse sentiment: {e}")
        return False

def test_language_detection():
    """Teste la détection de langue"""
    logger.info("🌍 Test détection de langue...")
    
    try:
        from langdetect import detect
        
        # Test détection français
        lang_fr = detect("Bonjour, comment allez-vous ?")
        if lang_fr == "fr":
            logger.info("✅ Détection français OK")
        else:
            logger.warning(f"⚠️ Détection français incertaine: {lang_fr}")
        
        # Test détection anglais
        lang_en = detect("Hello, how are you?")
        if lang_en == "en":
            logger.info("✅ Détection anglais OK")
        else:
            logger.warning(f"⚠️ Détection anglais incertaine: {lang_en}")
        
        logger.info("✅ Détection de langue fonctionnelle")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur détection langue: {e}")
        return False

def run_all_tests():
    """Exécute tous les tests"""
    logger.info("🚀 DÉBUT DES TESTS DE CONFIGURATION")
    logger.info("=" * 60)
    
    tests = [
        ("Version Python", test_python_version),
        ("Dépendances", test_dependencies),
        ("Données NLTK", test_nltk_data),
        ("MongoDB", test_mongodb_connection),
        ("Elasticsearch", test_elasticsearch_connection),
        ("Configuration", test_configuration_file),
        ("Analyse sentiment", test_sentiment_analysis),
        ("Détection langue", test_language_detection)
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"❌ Erreur inattendue dans {test_name}: {e}")
            results[test_name] = False
    
    # Résumé
    logger.info("\n" + "=" * 60)
    logger.info("📊 RÉSUMÉ DES TESTS")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSÉ" if result else "❌ ÉCHEC"
        logger.info(f"{test_name:<20} : {status}")
    
    logger.info(f"\nRésultat global: {passed}/{total} tests passés")
    
    if passed == total:
        logger.info("🎉 TOUS LES TESTS SONT PASSÉS !")
        logger.info("✅ Votre environnement est prêt pour l'analyse du harcèlement")
        return True
    else:
        logger.warning(f"⚠️ {total - passed} test(s) ont échoué")
        logger.info("💡 Consultez les messages ci-dessus pour résoudre les problèmes")
        return False

def main():
    """Fonction principale"""
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n⏹️ Tests interrompus par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
