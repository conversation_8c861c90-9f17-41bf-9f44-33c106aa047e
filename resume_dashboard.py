#!/usr/bin/env python3
"""
Résumé détaillé du Tableau de Bord Kibana
Affiche une représentation textuelle complète du dashboard
"""

from pymongo import MongoClient
from datetime import datetime
import pandas as pd

class ResumeDashboard:
    def __init__(self):
        """Initialise le générateur de résumé"""
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]
    
    def afficher_header(self):
        """Affiche l'en-tête du dashboard"""
        print("╔" + "═" * 78 + "╗")
        print("║" + " " * 20 + "📊 TABLEAU DE BORD KIBANA" + " " * 31 + "║")
        print("║" + " " * 15 + "ANALYSE DU HARCÈLEMENT EN LIGNE" + " " * 30 + "║")
        print("╚" + "═" * 78 + "╝")
        print()
    
    def afficher_viz_langues(self):
        """Affiche la visualisation des langues"""
        print("┌─────────────────────────────────────────┐")
        print("│          📊 RÉPARTITION DES LANGUES     │")
        print("├─────────────────────────────────────────┤")
        
        pipeline = [
            {"$match": {"nlp_effectue": True}},
            {"$group": {"_id": "$langue_nom", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        resultats = list(self.collection.aggregate(pipeline))
        total = sum(r["count"] for r in resultats)
        
        emojis = {"anglais": "🇺🇸", "français": "🇫🇷", "espagnol": "🇪🇸", "vi": "🇻🇳"}
        
        for result in resultats:
            langue = result["_id"]
            count = result["count"]
            pourcentage = (count / total * 100) if total > 0 else 0
            emoji = emojis.get(langue, "🌍")
            
            # Barre de progression
            barre_length = int(pourcentage / 3)  # Scale pour 30 chars max
            barre = "█" * barre_length + "░" * (30 - barre_length)
            
            print(f"│ {emoji} {langue:<10} │{barre}│ {count:3d} ({pourcentage:4.1f}%) │")
        
        print("└─────────────────────────────────────────┘")
        print()
    
    def afficher_viz_sentiments(self):
        """Affiche la visualisation des sentiments"""
        print("┌─────────────────────────────────────────┐")
        print("│         😊 RÉPARTITION DES SENTIMENTS   │")
        print("├─────────────────────────────────────────┤")
        
        pipeline = [
            {"$match": {"nlp_effectue": True}},
            {"$group": {"_id": "$sentiment", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        resultats = list(self.collection.aggregate(pipeline))
        total = sum(r["count"] for r in resultats)
        
        emojis = {"négatif": "😞", "neutre": "😐", "positif": "😊"}
        colors = {"négatif": "🔴", "neutre": "🟡", "positif": "🟢"}
        
        for result in resultats:
            sentiment = result["_id"]
            count = result["count"]
            pourcentage = (count / total * 100) if total > 0 else 0
            emoji = emojis.get(sentiment, "❓")
            color = colors.get(sentiment, "⚪")
            
            # Barre de progression colorée
            barre_length = int(pourcentage / 3)
            barre = color * barre_length + "⚪" * (30 - barre_length)
            
            print(f"│ {emoji} {sentiment:<8} │{barre}│ {count:3d} ({pourcentage:4.1f}%) │")
        
        print("└─────────────────────────────────────────┘")
        print()
    
    def afficher_viz_plateformes(self):
        """Affiche la visualisation des plateformes"""
        print("┌─────────────────────────────────────────┐")
        print("│        📱 RÉPARTITION PAR PLATEFORME    │")
        print("├─────────────────────────────────────────┤")
        
        pipeline = [
            {"$match": {"nlp_effectue": True}},
            {"$group": {"_id": "$plateforme", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        resultats = list(self.collection.aggregate(pipeline))
        total = sum(r["count"] for r in resultats)
        
        emojis = {"reddit": "🔴", "telegram": "🟡", "twitter": "🔵"}
        
        for result in resultats:
            plateforme = result["_id"]
            count = result["count"]
            pourcentage = (count / total * 100) if total > 0 else 0
            emoji = emojis.get(plateforme, "📱")
            
            # Barre de progression
            barre_length = int(pourcentage / 3)
            barre = "█" * barre_length + "░" * (30 - barre_length)
            
            print(f"│ {emoji} {plateforme:<9} │{barre}│ {count:3d} ({pourcentage:4.1f}%) │")
        
        print("└─────────────────────────────────────────┘")
        print()
    
    def afficher_evolution_temporelle(self):
        """Affiche l'évolution temporelle"""
        print("┌─────────────────────────────────────────────────────────────────────────────┐")
        print("│                    📈 ÉVOLUTION TEMPORELLE DES PUBLICATIONS                  │")
        print("├─────────────────────────────────────────────────────────────────────────────┤")
        
        # Agrégation par date
        pipeline = [
            {"$match": {"nlp_effectue": True, "date": {"$exists": True}}},
            {"$group": {
                "_id": {"$dateToString": {"format": "%Y-%m-%d", "date": "$date"}},
                "count": {"$sum": 1}
            }},
            {"$sort": {"_id": 1}}
        ]
        
        resultats = list(self.collection.aggregate(pipeline))
        
        if resultats:
            print("│ Top 5 jours avec le plus d'activité:                                       │")
            print("│                                                                             │")
            
            # Trier par count décroissant pour le top 5
            top_jours = sorted(resultats, key=lambda x: x["count"], reverse=True)[:5]
            
            for i, result in enumerate(top_jours, 1):
                date = result["_id"]
                count = result["count"]
                
                # Barre de progression relative au max
                max_count = top_jours[0]["count"]
                barre_length = int((count / max_count) * 40)
                barre = "█" * barre_length + "░" * (40 - barre_length)
                
                print(f"│ {i}. {date} │{barre}│ {count:3d} posts │")
            
            # Statistiques générales
            total_jours = len(resultats)
            total_posts = sum(r["count"] for r in resultats)
            moyenne = total_posts / total_jours if total_jours > 0 else 0
            
            print("│                                                                             │")
            print(f"│ 📅 Période: {total_jours} jours | 📊 Moyenne: {moyenne:.1f} posts/jour                    │")
        else:
            print("│ Aucune donnée temporelle disponible                                        │")
        
        print("└─────────────────────────────────────────────────────────────────────────────┘")
        print()
    
    def afficher_contenus_negatifs(self):
        """Affiche les contenus les plus négatifs"""
        print("┌─────────────────────────────────────────────────────────────────────────────┐")
        print("│                    😞 TOP 10 CONTENUS LES PLUS NÉGATIFS                     │")
        print("├─────────────────────────────────────────────────────────────────────────────┤")
        
        cursor = self.collection.find(
            {"nlp_effectue": True, "sentiment": "négatif"},
            {"titre": 1, "auteur": 1, "sentiment_score": 1, "plateforme": 1}
        ).sort("sentiment_score", 1).limit(10)
        
        print("│ #  │ Score  │ Plateforme │ Titre (extrait)                                │")
        print("├────┼────────┼────────────┼────────────────────────────────────────────────┤")
        
        for i, doc in enumerate(cursor, 1):
            titre = doc.get("titre", "")
            titre_court = titre[:45] + "..." if len(titre) > 45 else titre
            score = doc.get("sentiment_score", 0)
            plateforme = doc.get("plateforme", "N/A")
            
            emoji_platform = {"reddit": "🔴", "telegram": "🟡", "twitter": "🔵"}.get(plateforme, "📱")
            
            print(f"│{i:2d}  │{score:6.3f}  │ {emoji_platform} {plateforme:<7} │ {titre_court:<46} │")
        
        print("└─────────────────────────────────────────────────────────────────────────────┘")
        print()
    
    def afficher_filtres_interactifs(self):
        """Affiche les filtres interactifs disponibles"""
        print("┌─────────────────────────────────────────────────────────────────────────────┐")
        print("│                        🎛️ FILTRES INTERACTIFS DISPONIBLES                   │")
        print("├─────────────────────────────────────────────────────────────────────────────┤")
        
        # Filtres par langue
        langues = self.collection.distinct("langue_nom", {"nlp_effectue": True})
        print(f"│ 🌍 Langue ({len(langues)} options): {', '.join(langues):<50} │")
        
        # Filtres par sentiment
        sentiments = self.collection.distinct("sentiment", {"nlp_effectue": True})
        print(f"│ 😊 Sentiment ({len(sentiments)} options): {', '.join(sentiments):<47} │")
        
        # Filtres par plateforme
        plateformes = self.collection.distinct("plateforme", {"nlp_effectue": True})
        print(f"│ 📱 Plateforme ({len(plateformes)} options): {', '.join(plateformes):<45} │")
        
        # Plage de scores
        pipeline_scores = [
            {"$match": {"nlp_effectue": True, "sentiment_score": {"$exists": True}}},
            {"$group": {
                "_id": None,
                "min": {"$min": "$sentiment_score"},
                "max": {"$max": "$sentiment_score"}
            }}
        ]
        
        scores_result = list(self.collection.aggregate(pipeline_scores))
        if scores_result:
            score_min = scores_result[0]["min"]
            score_max = scores_result[0]["max"]
            print(f"│ 📊 Score sentiment: {score_min:.3f} à {score_max:.3f}                                    │")
        
        # Plage de dates
        pipeline_dates = [
            {"$match": {"nlp_effectue": True, "date": {"$exists": True}}},
            {"$group": {
                "_id": None,
                "min": {"$min": "$date"},
                "max": {"$max": "$date"}
            }}
        ]
        
        dates_result = list(self.collection.aggregate(pipeline_dates))
        if dates_result:
            date_min = dates_result[0]["min"].strftime("%Y-%m-%d")
            date_max = dates_result[0]["max"].strftime("%Y-%m-%d")
            print(f"│ 📅 Période: {date_min} à {date_max}                                        │")
        
        print("└─────────────────────────────────────────────────────────────────────────────┘")
        print()
    
    def afficher_resume_complet(self):
        """Affiche le résumé complet du dashboard"""
        self.afficher_header()
        
        # Statistiques globales
        total_docs = self.collection.count_documents({"nlp_effectue": True})
        print(f"📊 DONNÉES ANALYSÉES: {total_docs} documents de harcèlement en ligne")
        print("=" * 80)
        print()
        
        # Toutes les visualisations
        self.afficher_viz_langues()
        self.afficher_viz_sentiments()
        self.afficher_viz_plateformes()
        self.afficher_evolution_temporelle()
        self.afficher_contenus_negatifs()
        self.afficher_filtres_interactifs()
        
        # Footer
        print("╔" + "═" * 78 + "╗")
        print("║" + " " * 25 + "✅ DASHBOARD KIBANA COMPLET" + " " * 25 + "║")
        print("║" + " " * 15 + "Toutes les visualisations générées avec succès" + " " * 15 + "║")
        print("║" + " " * 20 + "Prêt pour analyse interactive" + " " * 27 + "║")
        print("╚" + "═" * 78 + "╝")
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()

def main():
    """Fonction principale"""
    resume = None
    
    try:
        resume = ResumeDashboard()
        resume.afficher_resume_complet()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        if resume:
            resume.fermer_connexions()

if __name__ == "__main__":
    main()
