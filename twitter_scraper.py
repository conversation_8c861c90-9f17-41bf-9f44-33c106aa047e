import tweepy
import logging
from datetime import datetime, timezone
import time
from typing import Dict, List, Optional
from config import Config
from database import DatabaseManager

logger = logging.getLogger(__name__)

class TwitterScraper:
    """Twitter scraper using official Twitter API v2"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager):
        self.config = config
        self.db_manager = db_manager
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Twitter API client"""
        try:
            if not self.config.TWITTER_BEARER_TOKEN:
                logger.warning("Twitter Bearer Token not provided. Twitter scraping disabled.")
                return
            
            # Initialize with Bear<PERSON>ken for API v2
            self.client = tweepy.Client(
                bearer_token=self.config.TWITTER_BEARER_TOKEN,
                consumer_key=self.config.TWITTER_API_KEY,
                consumer_secret=self.config.TWITTER_API_SECRET,
                access_token=self.config.TWITTER_ACCESS_TOKEN,
                access_token_secret=self.config.TWITTER_ACCESS_TOKEN_SECRET,
                wait_on_rate_limit=True
            )
            
            logger.info("Twitter API client initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Twitter client: {e}")
            self.client = None
    
    def search_tweets(self, query: str, max_results: int = 100) -> int:
        """Search for tweets containing specific keywords"""
        if not self.client:
            logger.warning("Twitter client not initialized. Skipping Twitter search.")
            return 0
        
        logger.info(f"Searching Twitter for: {query}")
        tweets_added = 0
        
        try:
            # Search for tweets
            tweets = tweepy.Paginator(
                self.client.search_recent_tweets,
                query=query,
                tweet_fields=['created_at', 'author_id', 'public_metrics', 'context_annotations'],
                user_fields=['username', 'name'],
                expansions=['author_id'],
                max_results=min(max_results, 100)  # API limit is 100 per request
            ).flatten(limit=max_results)
            
            # Process tweets
            for tweet in tweets:
                tweet_data = self._extract_tweet_data(tweet, query)
                
                if self.db_manager.insert_post(tweet_data):
                    tweets_added += 1
                    logger.info(f"Added tweet: {tweet.text[:60]}...")
                else:
                    logger.debug(f"Tweet already exists: {tweet.id}")
                
                # Rate limiting
                time.sleep(0.1)
                
        except tweepy.TooManyRequests:
            logger.warning("Twitter API rate limit exceeded. Waiting...")
            time.sleep(15 * 60)  # Wait 15 minutes
        except tweepy.Unauthorized:
            logger.error("Twitter API unauthorized. Check your credentials.")
        except Exception as e:
            logger.error(f"Error searching Twitter for '{query}': {str(e)}")
        
        logger.info(f"Finished searching Twitter for '{query}'. Added {tweets_added} new tweets")
        return tweets_added
    
    def _extract_tweet_data(self, tweet, search_query: str) -> Dict:
        """Extract relevant data from a tweet"""
        # Get author information
        author_username = "unknown"
        if hasattr(tweet, 'author_id') and tweet.author_id:
            try:
                user = self.client.get_user(id=tweet.author_id)
                if user.data:
                    author_username = user.data.username
            except:
                pass
        
        # Create tweet URL
        tweet_url = f"https://twitter.com/{author_username}/status/{tweet.id}"
        
        return {
            "title": tweet.text[:100] + "..." if len(tweet.text) > 100 else tweet.text,
            "content": tweet.text,
            "author": author_username,
            "date": tweet.created_at if tweet.created_at else datetime.now(timezone.utc),
            "url": tweet_url,
            "source": f"Twitter search: {search_query}",
            "platform": "twitter",
            "post_id": str(tweet.id),
            "retweet_count": getattr(tweet.public_metrics, 'retweet_count', 0) if hasattr(tweet, 'public_metrics') else 0,
            "like_count": getattr(tweet.public_metrics, 'like_count', 0) if hasattr(tweet, 'public_metrics') else 0,
            "reply_count": getattr(tweet.public_metrics, 'reply_count', 0) if hasattr(tweet, 'public_metrics') else 0
        }
    
    def search_harassment_keywords(self) -> Dict[str, int]:
        """Search for tweets using harassment-related keywords"""
        results = {}
        
        if not self.client:
            logger.warning("Twitter client not initialized. Skipping harassment keyword search.")
            return results
        
        for keyword in self.config.TWITTER_KEYWORDS:
            # Build search query with filters
            query = f'"{keyword}" -is:retweet lang:en'
            
            tweets_added = self.search_tweets(query, self.config.TWITTER_LIMIT_TWEETS)
            results[keyword] = tweets_added
            
            # Rate limiting between searches
            time.sleep(self.config.RATE_LIMIT_DELAY)
        
        return results
    
    def search_user_timeline(self, username: str, max_results: int = 100) -> int:
        """Search tweets from a specific user's timeline"""
        if not self.client:
            logger.warning("Twitter client not initialized. Skipping user timeline search.")
            return 0
        
        logger.info(f"Searching timeline for user: @{username}")
        tweets_added = 0
        
        try:
            # Get user ID
            user = self.client.get_user(username=username)
            if not user.data:
                logger.error(f"User @{username} not found")
                return 0
            
            user_id = user.data.id
            
            # Get user's tweets
            tweets = tweepy.Paginator(
                self.client.get_users_tweets,
                id=user_id,
                tweet_fields=['created_at', 'public_metrics'],
                max_results=min(max_results, 100)
            ).flatten(limit=max_results)
            
            for tweet in tweets:
                tweet_data = self._extract_tweet_data(tweet, f"@{username} timeline")
                
                if self.db_manager.insert_post(tweet_data):
                    tweets_added += 1
                    logger.info(f"Added tweet from @{username}: {tweet.text[:60]}...")
                
                time.sleep(0.1)  # Rate limiting
                
        except tweepy.NotFound:
            logger.error(f"User @{username} not found")
        except Exception as e:
            logger.error(f"Error searching timeline for @{username}: {str(e)}")
        
        logger.info(f"Finished searching @{username} timeline. Added {tweets_added} new tweets")
        return tweets_added
    
    def get_trending_topics(self, woeid: int = 1) -> List[str]:
        """Get trending topics (requires API v1.1)"""
        try:
            # This would require API v1.1 client
            # For now, return empty list
            logger.info("Trending topics feature not implemented yet")
            return []
        except Exception as e:
            logger.error(f"Error getting trending topics: {e}")
            return []
    
    def is_available(self) -> bool:
        """Check if Twitter scraper is available"""
        return self.client is not None
