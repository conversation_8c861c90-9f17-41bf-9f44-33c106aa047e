#!/usr/bin/env python3
"""
ÉTAPE 1 - Twitter Scraper
Collecte des données de harcèlement depuis Twitter/X
Stockage dans MongoDB : base 'harcelement', collection 'posts'
"""

import tweepy
from pymongo import MongoClient
from datetime import datetime, timezone
import time
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TwitterHarcelementScraper:
    def __init__(self):
        """Initialise le scraper Twitter"""
        # Configuration MongoDB (même base que Reddit)
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]

        # Configuration Twitter API - Clés intégrées
        self.bearer_token = "AAAAAAAAAAAAAAAAAAAAAGYW2QEAAAAA0mbJr8yz%2B%2FHj72yOUi4Zn%2FPCX4k%3DFrV9lygjcdk6Wxdf5U3E8lA0RsBA898ooa3efFbR6tmq9TABKz"
        self.api_key = "*************************"
        self.api_secret = "Ed6SLvgFqWWgV8jTDTMRUW4GCONoxCaALqHInNjnvIrWqV0ea6"
        self.access_token = "1930655343928811520-MXiWAbm0TlNsu7ig9OQ0pIP8QqwTxR"
        self.access_token_secret = "aGXKzAeVCZ9piCKCv0m40t9tQ0BrMkE9KBlUwVHSjYrwj"

        # Mots-clés ciblés pour le harcèlement
        self.mots_cles = ["harassment", "bullying", "cyberbullying", "online harassment"]
        self.limite_tweets = 30  # Tweets par mot-clé

        # Initialiser le client Twitter
        self.client = None
        self._initialiser_client()

        logger.info("Twitter Scraper initialisé")
        logger.info(f"Mots-clés ciblés: {self.mots_cles}")

    def _initialiser_client(self):
        """Initialise le client Twitter avec les vraies clés"""
        try:
            if self.bearer_token and self.bearer_token != "VOTRE_BEARER_TOKEN":
                self.client = tweepy.Client(
                    bearer_token=self.bearer_token,
                    consumer_key=self.api_key,
                    consumer_secret=self.api_secret,
                    access_token=self.access_token,
                    access_token_secret=self.access_token_secret,
                    wait_on_rate_limit=True
                )
                logger.info("✅ Client Twitter initialisé avec vraies clés API")
                return True
            else:
                logger.warning("⚠️ Clés Twitter manquantes - mode simulation activé")
                return False
        except Exception as e:
            logger.error(f"❌ Erreur initialisation Twitter: {e}")
            self.client = None
            return False
    
    def simuler_donnees_twitter(self, mot_cle, nombre=30):
        """Simule des données Twitter pour la démonstration"""
        logger.info(f"Simulation de données pour '{mot_cle}'")
        
        exemples_contenus = [
            f"Je subis du {mot_cle} au travail et je ne sais plus quoi faire...",
            f"Comment signaler du {mot_cle} en ligne ? Besoin d'aide urgente",
            f"Le {mot_cle} scolaire doit être pris au sérieux par les établissements",
            f"Témoignage : j'ai vécu du {mot_cle} pendant des années",
            f"Prévention du {mot_cle} : que peuvent faire les parents ?",
            f"Les réseaux sociaux facilitent le {mot_cle}, il faut agir",
            f"Victime de {mot_cle}, où trouver de l'aide psychologique ?",
            f"Le {mot_cle} en ligne a des conséquences réelles sur la santé mentale",
            f"Comment aider quelqu'un qui subit du {mot_cle} ?",
            f"Sensibilisation au {mot_cle} : parlons-en ouvertement"
        ]
        
        tweets_simules = []
        for i in range(nombre):
            contenu = exemples_contenus[i % len(exemples_contenus)]
            
            tweet_simule = {
                "titre": contenu[:100] + "..." if len(contenu) > 100 else contenu,
                "contenu": contenu,
                "auteur": f"user_twitter_{i+1}",
                "date": datetime.now(timezone.utc),
                "url": f"https://twitter.com/user_twitter_{i+1}/status/sim_{mot_cle}_{i+1}",
                "source": f"Twitter search: {mot_cle}",
                "plateforme": "twitter",
                "post_id": f"sim_{mot_cle}_{i+1}",
                "retweets": i % 10,
                "likes": i % 25,
                "date_collecte": datetime.now(timezone.utc)
            }
            tweets_simules.append(tweet_simule)
        
        return tweets_simules
    
    def extraire_donnees_tweet_reel(self, tweet, mot_cle):
        """Extrait les données d'un vrai tweet depuis l'API"""
        # Créer l'URL du tweet
        tweet_url = f"https://twitter.com/user/status/{tweet.id}"

        return {
            "titre": tweet.text[:100] + "..." if len(tweet.text) > 100 else tweet.text,
            "contenu": tweet.text,
            "auteur": f"user_{tweet.author_id}" if hasattr(tweet, 'author_id') else "unknown",
            "date": tweet.created_at if hasattr(tweet, 'created_at') else datetime.now(timezone.utc),
            "url": tweet_url,
            "source": f"Twitter search: {mot_cle}",
            "plateforme": "twitter",
            "post_id": str(tweet.id),
            "retweets": getattr(tweet.public_metrics, 'retweet_count', 0) if hasattr(tweet, 'public_metrics') else 0,
            "likes": getattr(tweet.public_metrics, 'like_count', 0) if hasattr(tweet, 'public_metrics') else 0,
            "date_collecte": datetime.now(timezone.utc)
        }

    def scraper_mot_cle(self, mot_cle):
        """Scrape les tweets pour un mot-clé spécifique"""
        logger.info(f"Début scraping Twitter pour: '{mot_cle}'")
        tweets_ajoutes = 0

        try:
            if self.client:
                # Utiliser l'API réelle Twitter
                logger.info(f"🔥 Utilisation de l'API Twitter réelle pour '{mot_cle}'")
                query = f'{mot_cle} -is:retweet lang:en'

                tweets = tweepy.Paginator(
                    self.client.search_recent_tweets,
                    query=query,
                    tweet_fields=['created_at', 'author_id', 'public_metrics'],
                    max_results=min(self.limite_tweets, 100)
                ).flatten(limit=self.limite_tweets)

                for tweet in tweets:
                    donnees_tweet = self.extraire_donnees_tweet_reel(tweet, mot_cle)

                    # Vérifier si le tweet existe déjà
                    if self.collection.count_documents({"url": donnees_tweet["url"]}) == 0:
                        self.collection.insert_one(donnees_tweet)
                        tweets_ajoutes += 1
                        logger.info(f"[+] Tweet réel ajouté: {tweet.text[:50]}...")
                    else:
                        logger.debug(f"[=] Tweet existant: {tweet.text[:50]}...")

                    time.sleep(0.1)  # Rate limiting
            else:
                # Mode simulation si pas d'API
                logger.info(f"⚠️ Mode simulation pour '{mot_cle}' (pas d'API)")
                tweets_simules = self.simuler_donnees_twitter(mot_cle, self.limite_tweets)

                for donnees_tweet in tweets_simules:
                    if self.collection.count_documents({"url": donnees_tweet["url"]}) == 0:
                        self.collection.insert_one(donnees_tweet)
                        tweets_ajoutes += 1
                        logger.info(f"[+] Tweet simulé ajouté: {donnees_tweet['titre'][:50]}...")

                    time.sleep(0.1)

        except Exception as e:
            logger.error(f"Erreur lors du scraping pour '{mot_cle}': {e}")

        logger.info(f"'{mot_cle}': {tweets_ajoutes} nouveaux tweets")
        return tweets_ajoutes
    
    def scraper_tous_mots_cles(self):
        """Scrape tous les mots-clés ciblés"""
        logger.info("=" * 60)
        logger.info("DÉBUT COLLECTE TWITTER - ÉTAPE 1")
        logger.info("=" * 60)
        
        debut = time.time()
        total_tweets = 0
        resultats = {}
        
        for mot_cle in self.mots_cles:
            tweets_ajoutes = self.scraper_mot_cle(mot_cle)
            resultats[f"Twitter:{mot_cle}"] = tweets_ajoutes
            total_tweets += tweets_ajoutes
            
            # Pause entre recherches
            time.sleep(1)
        
        fin = time.time()
        duree = fin - debut
        
        # Résumé
        logger.info("=" * 60)
        logger.info("RÉSUMÉ COLLECTE TWITTER")
        logger.info("=" * 60)
        logger.info(f"Durée totale: {duree:.2f} secondes")
        logger.info(f"Total nouveaux tweets: {total_tweets}")
        
        for source, count in resultats.items():
            logger.info(f"  {source}: {count} tweets")
        
        # Statistiques base de données
        total_db = self.collection.count_documents({"plateforme": "twitter"})
        logger.info(f"Total tweets en base: {total_db}")
        
        return resultats
    
    def obtenir_statistiques(self):
        """Obtient les statistiques Twitter de la collection"""
        logger.info("\nSTATISTIQUES TWITTER")
        logger.info("-" * 20)
        
        # Total tweets
        total_twitter = self.collection.count_documents({"plateforme": "twitter"})
        logger.info(f"Total tweets: {total_twitter}")
        
        # Par source Twitter
        pipeline_source = [
            {"$match": {"plateforme": "twitter"}},
            {"$group": {"_id": "$source", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        logger.info("Sources Twitter:")
        for result in self.collection.aggregate(pipeline_source):
            logger.info(f"  {result['_id']}: {result['count']} tweets")
        
        return total_twitter
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("Connexions fermées")

def main():
    """Fonction principale"""
    scraper = None
    
    try:
        # Initialiser le scraper
        scraper = TwitterHarcelementScraper()
        
        # Lancer la collecte
        resultats = scraper.scraper_tous_mots_cles()
        
        # Afficher les statistiques
        scraper.obtenir_statistiques()
        
        logger.info("✅ COLLECTE TWITTER TERMINÉE AVEC SUCCÈS")
        
    except KeyboardInterrupt:
        logger.info("Collecte interrompue par l'utilisateur")
    except Exception as e:
        logger.error(f"Erreur: {e}")
    finally:
        if scraper:
            scraper.fermer_connexions()

if __name__ == "__main__":
    main()
