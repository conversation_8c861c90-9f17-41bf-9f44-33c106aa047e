#!/usr/bin/env python3
"""
ÉTAPE 1 - Twitter Scraper
Collecte des données de harcèlement depuis Twitter/X
Stockage dans MongoDB : base 'harcelement', collection 'posts'
"""

from pymongo import MongoClient
from datetime import datetime, timezone
import time
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TwitterHarcelementScraper:
    def __init__(self):
        """Initialise le scraper Twitter"""
        # Configuration MongoDB (même base que Reddit)
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]
        
        # Mots-clés ciblés pour le harcèlement
        self.mots_cles = ["harassment", "bullying", "cyberbullying", "online harassment"]
        self.limite_tweets = 30  # Tweets par mot-clé
        
        logger.info("Twitter Scraper initialisé")
        logger.info(f"Mots-clés ciblés: {self.mots_cles}")
        logger.info("Mode simulation activé (remplacez par vraies clés API)")
    
    def simuler_donnees_twitter(self, mot_cle, nombre=30):
        """Simule des données Twitter pour la démonstration"""
        logger.info(f"Simulation de données pour '{mot_cle}'")
        
        exemples_contenus = [
            f"Je subis du {mot_cle} au travail et je ne sais plus quoi faire...",
            f"Comment signaler du {mot_cle} en ligne ? Besoin d'aide urgente",
            f"Le {mot_cle} scolaire doit être pris au sérieux par les établissements",
            f"Témoignage : j'ai vécu du {mot_cle} pendant des années",
            f"Prévention du {mot_cle} : que peuvent faire les parents ?",
            f"Les réseaux sociaux facilitent le {mot_cle}, il faut agir",
            f"Victime de {mot_cle}, où trouver de l'aide psychologique ?",
            f"Le {mot_cle} en ligne a des conséquences réelles sur la santé mentale",
            f"Comment aider quelqu'un qui subit du {mot_cle} ?",
            f"Sensibilisation au {mot_cle} : parlons-en ouvertement"
        ]
        
        tweets_simules = []
        for i in range(nombre):
            contenu = exemples_contenus[i % len(exemples_contenus)]
            
            tweet_simule = {
                "titre": contenu[:100] + "..." if len(contenu) > 100 else contenu,
                "contenu": contenu,
                "auteur": f"user_twitter_{i+1}",
                "date": datetime.now(timezone.utc),
                "url": f"https://twitter.com/user_twitter_{i+1}/status/sim_{mot_cle}_{i+1}",
                "source": f"Twitter search: {mot_cle}",
                "plateforme": "twitter",
                "post_id": f"sim_{mot_cle}_{i+1}",
                "retweets": i % 10,
                "likes": i % 25,
                "date_collecte": datetime.now(timezone.utc)
            }
            tweets_simules.append(tweet_simule)
        
        return tweets_simules
    
    def scraper_mot_cle(self, mot_cle):
        """Scrape les tweets pour un mot-clé spécifique"""
        logger.info(f"Début scraping Twitter pour: '{mot_cle}'")
        tweets_ajoutes = 0
        
        try:
            # Mode simulation (remplacez par l'API réelle)
            tweets_simules = self.simuler_donnees_twitter(mot_cle, self.limite_tweets)
            
            for donnees_tweet in tweets_simules:
                # Vérifier si le tweet simulé existe déjà
                if self.collection.count_documents({"url": donnees_tweet["url"]}) == 0:
                    self.collection.insert_one(donnees_tweet)
                    tweets_ajoutes += 1
                    logger.info(f"[+] Tweet ajouté: {donnees_tweet['titre'][:50]}...")
                else:
                    logger.debug(f"[=] Tweet existant: {donnees_tweet['titre'][:50]}...")
                
                time.sleep(0.1)  # Simulation du rate limiting
                
        except Exception as e:
            logger.error(f"Erreur lors du scraping pour '{mot_cle}': {e}")
        
        logger.info(f"'{mot_cle}': {tweets_ajoutes} nouveaux tweets")
        return tweets_ajoutes
    
    def scraper_tous_mots_cles(self):
        """Scrape tous les mots-clés ciblés"""
        logger.info("=" * 60)
        logger.info("DÉBUT COLLECTE TWITTER - ÉTAPE 1")
        logger.info("=" * 60)
        
        debut = time.time()
        total_tweets = 0
        resultats = {}
        
        for mot_cle in self.mots_cles:
            tweets_ajoutes = self.scraper_mot_cle(mot_cle)
            resultats[f"Twitter:{mot_cle}"] = tweets_ajoutes
            total_tweets += tweets_ajoutes
            
            # Pause entre recherches
            time.sleep(1)
        
        fin = time.time()
        duree = fin - debut
        
        # Résumé
        logger.info("=" * 60)
        logger.info("RÉSUMÉ COLLECTE TWITTER")
        logger.info("=" * 60)
        logger.info(f"Durée totale: {duree:.2f} secondes")
        logger.info(f"Total nouveaux tweets: {total_tweets}")
        
        for source, count in resultats.items():
            logger.info(f"  {source}: {count} tweets")
        
        # Statistiques base de données
        total_db = self.collection.count_documents({"plateforme": "twitter"})
        logger.info(f"Total tweets en base: {total_db}")
        
        return resultats
    
    def obtenir_statistiques(self):
        """Obtient les statistiques Twitter de la collection"""
        logger.info("\nSTATISTIQUES TWITTER")
        logger.info("-" * 20)
        
        # Total tweets
        total_twitter = self.collection.count_documents({"plateforme": "twitter"})
        logger.info(f"Total tweets: {total_twitter}")
        
        # Par source Twitter
        pipeline_source = [
            {"$match": {"plateforme": "twitter"}},
            {"$group": {"_id": "$source", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        logger.info("Sources Twitter:")
        for result in self.collection.aggregate(pipeline_source):
            logger.info(f"  {result['_id']}: {result['count']} tweets")
        
        return total_twitter
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("Connexions fermées")

def main():
    """Fonction principale"""
    scraper = None
    
    try:
        # Initialiser le scraper
        scraper = TwitterHarcelementScraper()
        
        # Lancer la collecte
        resultats = scraper.scraper_tous_mots_cles()
        
        # Afficher les statistiques
        scraper.obtenir_statistiques()
        
        logger.info("✅ COLLECTE TWITTER TERMINÉE AVEC SUCCÈS")
        
    except KeyboardInterrupt:
        logger.info("Collecte interrompue par l'utilisateur")
    except Exception as e:
        logger.error(f"Erreur: {e}")
    finally:
        if scraper:
            scraper.fermer_connexions()

if __name__ == "__main__":
    main()
