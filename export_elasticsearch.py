#!/usr/bin/env python3
"""
Export des données Elasticsearch
Exporte les données indexées depuis Elasticsearch vers différents formats
"""

import json
import csv
import logging
from datetime import datetime
from pathlib import Path
import pandas as pd
from pymongo import MongoClient

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ElasticsearchExporter:
    def __init__(self, index_name="harcelement_posts"):
        """Initialise l'exporteur Elasticsearch (mode simulation)"""
        # En mode simulation, on utilise MongoDB comme source
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]
        
        self.index_name = index_name
        
        # Créer le dossier d'export
        self.export_dir = Path("exports")
        self.export_dir.mkdir(exist_ok=True)
        
        logger.info(f"🔍 Exporteur Elasticsearch (simulation)")
        logger.info(f"📊 Index: {index_name}")
        logger.info(f"📁 Dossier d'export: {self.export_dir}")
    
    def simuler_donnees_elasticsearch(self):
        """Simule les données comme si elles venaient d'Elasticsearch"""
        cursor = self.collection.find({"nlp_effectue": True})
        documents_es = []
        
        for doc in cursor:
            # Convertir au format Elasticsearch
            doc_es = {
                "_index": self.index_name,
                "_id": str(doc["_id"]),
                "_source": {
                    # Champs requis par l'énoncé
                    "titre": doc.get("titre", ""),
                    "contenu": doc.get("contenu", ""),
                    "auteur": doc.get("auteur", ""),
                    "date": doc.get("date").isoformat() if doc.get("date") else None,
                    "url": doc.get("url", ""),
                    "langue": doc.get("langue_code", ""),
                    "sentiment": doc.get("sentiment", ""),
                    "score": doc.get("sentiment_score", 0.0),
                    
                    # Champs enrichis
                    "langue_nom": doc.get("langue_nom", ""),
                    "plateforme": doc.get("plateforme", ""),
                    "source": doc.get("source", ""),
                    "sentiment_vader": doc.get("sentiment_vader", {}),
                    "sentiment_textblob": doc.get("sentiment_textblob", {}),
                    "date_indexation": datetime.now().isoformat()
                }
            }
            documents_es.append(doc_es)
        
        return documents_es
    
    def export_json_elasticsearch(self, filename=None):
        """Exporte les données au format Elasticsearch JSON"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"elasticsearch_export_{timestamp}.json"
        
        filepath = self.export_dir / filename
        
        logger.info(f"📄 Export Elasticsearch JSON vers: {filepath}")
        
        # Simuler les données Elasticsearch
        documents_es = self.simuler_donnees_elasticsearch()
        
        # Format d'export Elasticsearch
        export_data = {
            "metadata": {
                "export_date": datetime.now().isoformat(),
                "index_name": self.index_name,
                "total_documents": len(documents_es),
                "elasticsearch_version": "8.0.0",
                "export_type": "simulation"
            },
            "documents": documents_es
        }
        
        # Sauvegarder
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Export Elasticsearch JSON terminé: {len(documents_es)} documents")
        return filepath
    
    def export_ndjson_elasticsearch(self, filename=None):
        """Exporte au format NDJSON (Newline Delimited JSON) pour Elasticsearch"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"elasticsearch_bulk_{timestamp}.ndjson"
        
        filepath = self.export_dir / filename
        
        logger.info(f"📄 Export Elasticsearch NDJSON vers: {filepath}")
        
        # Simuler les données Elasticsearch
        documents_es = self.simuler_donnees_elasticsearch()
        
        # Format NDJSON pour bulk import
        with open(filepath, 'w', encoding='utf-8') as f:
            for doc in documents_es:
                # Action d'index
                action = {
                    "index": {
                        "_index": doc["_index"],
                        "_id": doc["_id"]
                    }
                }
                f.write(json.dumps(action, ensure_ascii=False) + '\n')
                
                # Document source
                f.write(json.dumps(doc["_source"], ensure_ascii=False) + '\n')
        
        logger.info(f"✅ Export NDJSON terminé: {len(documents_es)} documents")
        logger.info(f"💡 Import avec: curl -X POST 'localhost:9200/_bulk' -H 'Content-Type: application/json' --data-binary @{filename}")
        return filepath
    
    def export_csv_elasticsearch(self, filename=None):
        """Exporte les données Elasticsearch en CSV"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"elasticsearch_export_{timestamp}.csv"
        
        filepath = self.export_dir / filename
        
        logger.info(f"📊 Export Elasticsearch CSV vers: {filepath}")
        
        # Simuler les données Elasticsearch
        documents_es = self.simuler_donnees_elasticsearch()
        
        # Préparer les données pour CSV
        csv_data = []
        for doc in documents_es:
            source = doc["_source"]
            row = {
                "elasticsearch_id": doc["_id"],
                "index": doc["_index"],
                "titre": source.get("titre", ""),
                "auteur": source.get("auteur", ""),
                "date": source.get("date", ""),
                "url": source.get("url", ""),
                "langue": source.get("langue", ""),
                "langue_nom": source.get("langue_nom", ""),
                "sentiment": source.get("sentiment", ""),
                "score": source.get("score", 0),
                "plateforme": source.get("plateforme", ""),
                "source": source.get("source", ""),
                "vader_compound": source.get("sentiment_vader", {}).get("compound", 0),
                "textblob_polarity": source.get("sentiment_textblob", {}).get("polarity", 0),
                "date_indexation": source.get("date_indexation", "")
            }
            csv_data.append(row)
        
        # Sauvegarder en CSV
        if csv_data:
            fieldnames = csv_data[0].keys()
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(csv_data)
        
        logger.info(f"✅ Export CSV terminé: {len(csv_data)} documents")
        return filepath
    
    def export_agregations_json(self, filename=None):
        """Exporte les agrégations Elasticsearch simulées"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"elasticsearch_agregations_{timestamp}.json"
        
        filepath = self.export_dir / filename
        
        logger.info(f"📊 Export agrégations Elasticsearch vers: {filepath}")
        
        # Simuler les agrégations Elasticsearch
        documents_es = self.simuler_donnees_elasticsearch()
        
        # Agrégation par sentiment
        agg_sentiment = {}
        for doc in documents_es:
            sentiment = doc["_source"].get("sentiment", "unknown")
            agg_sentiment[sentiment] = agg_sentiment.get(sentiment, 0) + 1
        
        # Agrégation par langue
        agg_langue = {}
        for doc in documents_es:
            langue = doc["_source"].get("langue_nom", "unknown")
            agg_langue[langue] = agg_langue.get(langue, 0) + 1
        
        # Agrégation par plateforme
        agg_plateforme = {}
        for doc in documents_es:
            plateforme = doc["_source"].get("plateforme", "unknown")
            agg_plateforme[plateforme] = agg_plateforme.get(plateforme, 0) + 1
        
        # Statistiques des scores
        scores = [doc["_source"].get("score", 0) for doc in documents_es]
        stats_scores = {
            "min": min(scores) if scores else 0,
            "max": max(scores) if scores else 0,
            "avg": sum(scores) / len(scores) if scores else 0,
            "count": len(scores)
        }
        
        # Histogramme des scores
        histogram_scores = {
            "très_négatif": len([s for s in scores if s <= -0.5]),
            "négatif": len([s for s in scores if -0.5 < s <= -0.1]),
            "neutre": len([s for s in scores if -0.1 < s < 0.1]),
            "positif": len([s for s in scores if 0.1 <= s < 0.5]),
            "très_positif": len([s for s in scores if s >= 0.5])
        }
        
        # Format agrégations Elasticsearch
        agregations = {
            "metadata": {
                "export_date": datetime.now().isoformat(),
                "index_name": self.index_name,
                "total_documents": len(documents_es),
                "type": "elasticsearch_aggregations_simulation"
            },
            "aggregations": {
                "sentiment_distribution": {
                    "buckets": [
                        {"key": k, "doc_count": v} 
                        for k, v in sorted(agg_sentiment.items(), key=lambda x: x[1], reverse=True)
                    ]
                },
                "langue_distribution": {
                    "buckets": [
                        {"key": k, "doc_count": v}
                        for k, v in sorted(agg_langue.items(), key=lambda x: x[1], reverse=True)
                    ]
                },
                "plateforme_distribution": {
                    "buckets": [
                        {"key": k, "doc_count": v}
                        for k, v in sorted(agg_plateforme.items(), key=lambda x: x[1], reverse=True)
                    ]
                },
                "score_stats": stats_scores,
                "score_histogram": {
                    "buckets": [
                        {"key": k, "doc_count": v}
                        for k, v in histogram_scores.items()
                    ]
                }
            }
        }
        
        # Sauvegarder
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(agregations, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Export agrégations terminé")
        return filepath
    
    def export_mapping_elasticsearch(self, filename=None):
        """Exporte le mapping Elasticsearch"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"elasticsearch_mapping_{timestamp}.json"
        
        filepath = self.export_dir / filename
        
        logger.info(f"🗺️ Export mapping Elasticsearch vers: {filepath}")
        
        # Mapping Elasticsearch optimisé
        mapping = {
            "metadata": {
                "export_date": datetime.now().isoformat(),
                "index_name": self.index_name,
                "elasticsearch_version": "8.0.0"
            },
            "mapping": {
                "properties": {
                    # Champs principaux requis
                    "titre": {
                        "type": "text",
                        "analyzer": "standard",
                        "fields": {
                            "keyword": {"type": "keyword", "ignore_above": 256}
                        }
                    },
                    "contenu": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    "auteur": {
                        "type": "keyword"
                    },
                    "date": {
                        "type": "date",
                        "format": "strict_date_optional_time||epoch_millis"
                    },
                    "url": {
                        "type": "keyword",
                        "index": False
                    },
                    "langue": {
                        "type": "keyword"
                    },
                    "sentiment": {
                        "type": "keyword"
                    },
                    "score": {
                        "type": "float"
                    },
                    
                    # Champs enrichis
                    "langue_nom": {
                        "type": "keyword"
                    },
                    "plateforme": {
                        "type": "keyword"
                    },
                    "source": {
                        "type": "keyword"
                    },
                    "sentiment_vader": {
                        "properties": {
                            "compound": {"type": "float"},
                            "pos": {"type": "float"},
                            "neu": {"type": "float"},
                            "neg": {"type": "float"}
                        }
                    },
                    "sentiment_textblob": {
                        "properties": {
                            "polarity": {"type": "float"},
                            "subjectivity": {"type": "float"}
                        }
                    },
                    "date_indexation": {
                        "type": "date"
                    }
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "custom_text_analyzer": {
                            "type": "custom",
                            "tokenizer": "standard",
                            "filter": ["lowercase", "stop"]
                        }
                    }
                }
            }
        }
        
        # Sauvegarder
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(mapping, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Export mapping terminé")
        return filepath
    
    def export_requetes_exemples(self, filename=None):
        """Exporte des exemples de requêtes Elasticsearch"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"elasticsearch_requetes_{timestamp}.json"
        
        filepath = self.export_dir / filename
        
        logger.info(f"🔍 Export requêtes exemples vers: {filepath}")
        
        requetes_exemples = {
            "metadata": {
                "export_date": datetime.now().isoformat(),
                "index_name": self.index_name,
                "description": "Exemples de requêtes Elasticsearch pour l'analyse du harcèlement"
            },
            "requetes": {
                "recherche_sentiment_negatif": {
                    "description": "Rechercher tous les contenus négatifs",
                    "query": {
                        "term": {"sentiment": "négatif"}
                    }
                },
                "recherche_textuelle": {
                    "description": "Recherche textuelle dans titre et contenu",
                    "query": {
                        "multi_match": {
                            "query": "bullying harassment",
                            "fields": ["titre^2", "contenu"]
                        }
                    }
                },
                "filtrage_par_langue": {
                    "description": "Filtrer par langue anglaise",
                    "query": {
                        "bool": {
                            "filter": [
                                {"term": {"langue": "en"}}
                            ]
                        }
                    }
                },
                "agregation_par_plateforme": {
                    "description": "Agrégation par plateforme",
                    "query": {"match_all": {}},
                    "aggs": {
                        "plateformes": {
                            "terms": {"field": "plateforme"}
                        }
                    },
                    "size": 0
                },
                "score_range_query": {
                    "description": "Documents avec score très négatif",
                    "query": {
                        "range": {
                            "score": {
                                "lte": -0.5
                            }
                        }
                    }
                },
                "date_range_query": {
                    "description": "Documents des 30 derniers jours",
                    "query": {
                        "range": {
                            "date": {
                                "gte": "now-30d"
                            }
                        }
                    }
                },
                "agregation_temporelle": {
                    "description": "Évolution temporelle par mois",
                    "query": {"match_all": {}},
                    "aggs": {
                        "evolution_mensuelle": {
                            "date_histogram": {
                                "field": "date",
                                "calendar_interval": "month"
                            },
                            "aggs": {
                                "sentiment_moyen": {
                                    "avg": {"field": "score"}
                                }
                            }
                        }
                    },
                    "size": 0
                },
                "recherche_complexe": {
                    "description": "Recherche complexe avec filtres multiples",
                    "query": {
                        "bool": {
                            "must": [
                                {"match": {"contenu": "bullying"}}
                            ],
                            "filter": [
                                {"term": {"sentiment": "négatif"}},
                                {"range": {"score": {"lte": -0.3}}}
                            ],
                            "must_not": [
                                {"term": {"plateforme": "spam"}}
                            ]
                        }
                    }
                }
            }
        }
        
        # Sauvegarder
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(requetes_exemples, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Export requêtes exemples terminé")
        return filepath
    
    def export_tout(self):
        """Exporte toutes les données Elasticsearch simulées"""
        logger.info("🚀 EXPORT COMPLET ELASTICSEARCH (SIMULATION)")
        logger.info("=" * 60)
        
        exports_realises = []
        
        try:
            # 1. JSON Elasticsearch
            logger.info("\n1️⃣ Export JSON Elasticsearch...")
            filepath = self.export_json_elasticsearch()
            exports_realises.append(("JSON Elasticsearch", filepath))
            
            # 2. NDJSON pour bulk import
            logger.info("\n2️⃣ Export NDJSON bulk...")
            filepath = self.export_ndjson_elasticsearch()
            exports_realises.append(("NDJSON Bulk", filepath))
            
            # 3. CSV
            logger.info("\n3️⃣ Export CSV...")
            filepath = self.export_csv_elasticsearch()
            exports_realises.append(("CSV", filepath))
            
            # 4. Agrégations
            logger.info("\n4️⃣ Export agrégations...")
            filepath = self.export_agregations_json()
            exports_realises.append(("Agrégations", filepath))
            
            # 5. Mapping
            logger.info("\n5️⃣ Export mapping...")
            filepath = self.export_mapping_elasticsearch()
            exports_realises.append(("Mapping", filepath))
            
            # 6. Requêtes exemples
            logger.info("\n6️⃣ Export requêtes exemples...")
            filepath = self.export_requetes_exemples()
            exports_realises.append(("Requêtes Exemples", filepath))
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'export: {e}")
            return False
        
        # Résumé
        logger.info("\n" + "=" * 60)
        logger.info("📋 RÉSUMÉ DES EXPORTS ELASTICSEARCH")
        logger.info("=" * 60)
        
        for nom, filepath in exports_realises:
            taille = filepath.stat().st_size / 1024  # KB
            logger.info(f"✅ {nom:<20} : {filepath.name} ({taille:.1f} KB)")
        
        logger.info(f"\n📁 Tous les fichiers dans: {self.export_dir}")
        logger.info(f"🎉 {len(exports_realises)} exports réalisés avec succès !")
        
        return True
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("🔌 Connexions fermées")

def main():
    """Fonction principale"""
    exporter = None
    
    try:
        # Initialiser l'exporteur
        exporter = ElasticsearchExporter()
        
        # Exporter toutes les données
        success = exporter.export_tout()
        
        if success:
            logger.info("\n✅ EXPORT ELASTICSEARCH TERMINÉ AVEC SUCCÈS !")
        else:
            logger.error("\n❌ ERREURS LORS DE L'EXPORT ELASTICSEARCH")
        
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
    finally:
        if exporter:
            exporter.fermer_connexions()

if __name__ == "__main__":
    main()
