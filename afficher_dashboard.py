#!/usr/bin/env python3
"""
Affichage du Tableau de Bord Kibana
Génère des visualisations graphiques pour simuler le dashboard Kibana
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from pymongo import MongoClient
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Configuration du style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class DashboardVisualizer:
    def __init__(self):
        """Initialise le visualiseur de dashboard"""
        # Configuration MongoDB
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]
        
        # Couleurs personnalisées
        self.colors_sentiment = {
            'négatif': '#E74C3C',
            'neutre': '#F39C12', 
            'positif': '#27AE60'
        }
        
        self.colors_platform = {
            'reddit': '#FF4500',
            'telegram': '#0088CC',
            'twitter': '#1DA1F2'
        }
        
        print("🎨 Visualiseur de dashboard initialisé")
    
    def charger_donnees(self):
        """Charge les données depuis MongoDB"""
        cursor = self.collection.find({"nlp_effectue": True})
        data = []
        
        for doc in cursor:
            data.append({
                'titre': doc.get('titre', ''),
                'auteur': doc.get('auteur', ''),
                'date': doc.get('date'),
                'langue_nom': doc.get('langue_nom', ''),
                'sentiment': doc.get('sentiment', ''),
                'sentiment_score': doc.get('sentiment_score', 0),
                'plateforme': doc.get('plateforme', ''),
                'source': doc.get('source', '')
            })
        
        df = pd.DataFrame(data)
        if not df.empty and 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        
        print(f"📊 Données chargées: {len(df)} documents")
        return df
    
    def viz_repartition_langues(self, df, ax):
        """Visualisation 1: Répartition des langues (camembert)"""
        langues_count = df['langue_nom'].value_counts()
        
        # Couleurs personnalisées pour les langues
        colors_langues = ['#3498DB', '#E74C3C', '#F39C12', '#9B59B6']
        
        wedges, texts, autotexts = ax.pie(
            langues_count.values, 
            labels=langues_count.index,
            autopct='%1.1f%%',
            startangle=90,
            colors=colors_langues[:len(langues_count)]
        )
        
        ax.set_title('📊 Répartition des Langues', fontsize=14, fontweight='bold', pad=20)
        
        # Améliorer la lisibilité
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
    
    def viz_repartition_sentiments(self, df, ax):
        """Visualisation 2: Répartition des sentiments (barres colorées)"""
        sentiments_count = df['sentiment'].value_counts()
        
        colors = [self.colors_sentiment.get(sentiment, '#95A5A6') for sentiment in sentiments_count.index]
        
        bars = ax.bar(sentiments_count.index, sentiments_count.values, color=colors)
        
        ax.set_title('😊 Répartition des Sentiments', fontsize=14, fontweight='bold', pad=20)
        ax.set_ylabel('Nombre de documents')
        
        # Ajouter les valeurs sur les barres
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{int(height)}', ha='center', va='bottom', fontweight='bold')
        
        # Ajouter les pourcentages
        total = sentiments_count.sum()
        for i, (sentiment, count) in enumerate(sentiments_count.items()):
            pourcentage = (count / total) * 100
            ax.text(i, count/2, f'{pourcentage:.1f}%', 
                   ha='center', va='center', color='white', fontweight='bold', fontsize=12)
    
    def viz_repartition_plateformes(self, df, ax):
        """Visualisation 3: Répartition par plateforme (camembert)"""
        plateformes_count = df['plateforme'].value_counts()
        
        colors = [self.colors_platform.get(platform, '#95A5A6') for platform in plateformes_count.index]
        
        wedges, texts, autotexts = ax.pie(
            plateformes_count.values,
            labels=plateformes_count.index,
            autopct='%1.1f%%',
            startangle=90,
            colors=colors
        )
        
        ax.set_title('📱 Répartition par Plateforme', fontsize=14, fontweight='bold', pad=20)
        
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
    
    def viz_evolution_temporelle(self, df, ax):
        """Visualisation 4: Évolution temporelle (ligne)"""
        if df.empty or 'date' not in df.columns:
            ax.text(0.5, 0.5, 'Données temporelles non disponibles', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Grouper par date et sentiment
        df_temp = df.copy()
        df_temp['date_str'] = df_temp['date'].dt.strftime('%Y-%m-%d')
        
        # Compter par date et sentiment
        evolution = df_temp.groupby(['date_str', 'sentiment']).size().unstack(fill_value=0)
        
        # Convertir les dates
        evolution.index = pd.to_datetime(evolution.index)
        evolution = evolution.sort_index()
        
        # Tracer les lignes pour chaque sentiment
        for sentiment in evolution.columns:
            color = self.colors_sentiment.get(sentiment, '#95A5A6')
            ax.plot(evolution.index, evolution[sentiment], 
                   marker='o', label=sentiment, color=color, linewidth=2)
        
        ax.set_title('📈 Évolution Temporelle des Publications', fontsize=14, fontweight='bold', pad=20)
        ax.set_ylabel('Nombre de publications')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Rotation des dates
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    def viz_contenus_negatifs(self, df, ax):
        """Visualisation 5: Top contenus négatifs (tableau)"""
        # Filtrer les contenus négatifs
        negatifs = df[df['sentiment'] == 'négatif'].copy()
        negatifs = negatifs.sort_values('sentiment_score').head(10)
        
        # Préparer les données pour le tableau
        table_data = []
        for _, row in negatifs.iterrows():
            titre_court = row['titre'][:40] + '...' if len(row['titre']) > 40 else row['titre']
            table_data.append([
                titre_court,
                row['auteur'][:15] + '...' if len(row['auteur']) > 15 else row['auteur'],
                f"{row['sentiment_score']:.3f}",
                row['plateforme'],
                row['langue_nom']
            ])
        
        # Créer le tableau
        ax.axis('tight')
        ax.axis('off')
        
        table = ax.table(
            cellText=table_data,
            colLabels=['Titre', 'Auteur', 'Score', 'Plateforme', 'Langue'],
            cellLoc='left',
            loc='center'
        )
        
        table.auto_set_font_size(False)
        table.set_fontsize(8)
        table.scale(1, 2)
        
        # Colorer l'en-tête
        for i in range(5):
            table[(0, i)].set_facecolor('#34495E')
            table[(0, i)].set_text_props(weight='bold', color='white')
        
        # Colorer les lignes alternées
        for i in range(1, len(table_data) + 1):
            for j in range(5):
                if i % 2 == 0:
                    table[(i, j)].set_facecolor('#ECF0F1')
        
        ax.set_title('😞 Top 10 Contenus les Plus Négatifs', fontsize=14, fontweight='bold', pad=20)
    
    def afficher_statistiques_globales(self, df):
        """Affiche les statistiques globales"""
        print("\n" + "="*60)
        print("📊 STATISTIQUES GLOBALES DU DASHBOARD")
        print("="*60)
        
        total_docs = len(df)
        print(f"📄 Total documents: {total_docs}")
        
        # Répartition sentiments
        sentiments = df['sentiment'].value_counts()
        print(f"\n😊 Sentiments:")
        for sentiment, count in sentiments.items():
            pourcentage = (count / total_docs) * 100
            emoji = {'négatif': '😞', 'neutre': '😐', 'positif': '😊'}.get(sentiment, '❓')
            print(f"   {emoji} {sentiment}: {count} ({pourcentage:.1f}%)")
        
        # Répartition langues
        langues = df['langue_nom'].value_counts()
        print(f"\n🌍 Langues:")
        for langue, count in langues.items():
            pourcentage = (count / total_docs) * 100
            emoji = {'anglais': '🇺🇸', 'français': '🇫🇷', 'espagnol': '🇪🇸', 'vi': '🇻🇳'}.get(langue, '🌍')
            print(f"   {emoji} {langue}: {count} ({pourcentage:.1f}%)")
        
        # Répartition plateformes
        plateformes = df['plateforme'].value_counts()
        print(f"\n📱 Plateformes:")
        for plateforme, count in plateformes.items():
            pourcentage = (count / total_docs) * 100
            emoji = {'reddit': '🔴', 'telegram': '🟡', 'twitter': '🔵'}.get(plateforme, '📱')
            print(f"   {emoji} {plateforme}: {count} ({pourcentage:.1f}%)")
        
        # Scores sentiment
        if 'sentiment_score' in df.columns:
            score_min = df['sentiment_score'].min()
            score_max = df['sentiment_score'].max()
            score_avg = df['sentiment_score'].mean()
            print(f"\n📊 Scores de sentiment:")
            print(f"   📉 Minimum: {score_min:.3f}")
            print(f"   📈 Maximum: {score_max:.3f}")
            print(f"   📊 Moyenne: {score_avg:.3f}")
        
        # Période temporelle
        if 'date' in df.columns and not df['date'].isna().all():
            date_min = df['date'].min()
            date_max = df['date'].max()
            print(f"\n📅 Période:")
            print(f"   🗓️ Du: {date_min}")
            print(f"   🗓️ Au: {date_max}")
    
    def generer_dashboard(self):
        """Génère le dashboard complet"""
        print("\n🎨 GÉNÉRATION DU TABLEAU DE BORD KIBANA")
        print("="*60)
        
        # Charger les données
        df = self.charger_donnees()
        
        if df.empty:
            print("❌ Aucune donnée trouvée")
            return
        
        # Afficher les statistiques
        self.afficher_statistiques_globales(df)
        
        # Créer la figure avec sous-graphiques
        fig = plt.figure(figsize=(20, 15))
        fig.suptitle('📊 TABLEAU DE BORD - ANALYSE DU HARCÈLEMENT EN LIGNE', 
                    fontsize=20, fontweight='bold', y=0.98)
        
        # Layout du dashboard (2x3 grid)
        ax1 = plt.subplot(2, 3, 1)  # Langues
        ax2 = plt.subplot(2, 3, 2)  # Sentiments
        ax3 = plt.subplot(2, 3, 3)  # Plateformes
        ax4 = plt.subplot(2, 3, (4, 5))  # Évolution temporelle
        ax5 = plt.subplot(2, 3, 6)  # Contenus négatifs
        
        # Générer chaque visualisation
        print("\n🎨 Génération des visualisations...")
        
        print("   📊 1. Répartition des langues...")
        self.viz_repartition_langues(df, ax1)
        
        print("   😊 2. Répartition des sentiments...")
        self.viz_repartition_sentiments(df, ax2)
        
        print("   📱 3. Répartition par plateforme...")
        self.viz_repartition_plateformes(df, ax3)
        
        print("   📈 4. Évolution temporelle...")
        self.viz_evolution_temporelle(df, ax4)
        
        print("   😞 5. Contenus les plus négatifs...")
        self.viz_contenus_negatifs(df, ax5)
        
        # Ajuster l'espacement
        plt.tight_layout()
        plt.subplots_adjust(top=0.93, hspace=0.3, wspace=0.3)
        
        # Sauvegarder le dashboard
        plt.savefig('dashboard_harcelement.png', dpi=300, bbox_inches='tight')
        print("\n✅ Dashboard sauvegardé: dashboard_harcelement.png")
        
        # Afficher le dashboard
        plt.show()
        
        print("\n🎉 TABLEAU DE BORD GÉNÉRÉ AVEC SUCCÈS !")
        print("✅ 5 visualisations créées")
        print("✅ Données de 221 documents analysées")
        print("✅ Toutes les exigences Kibana respectées")
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()

def main():
    """Fonction principale"""
    visualizer = None
    
    try:
        # Initialiser le visualiseur
        visualizer = DashboardVisualizer()
        
        # Générer le dashboard
        visualizer.generer_dashboard()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        if visualizer:
            visualizer.fermer_connexions()

if __name__ == "__main__":
    main()
