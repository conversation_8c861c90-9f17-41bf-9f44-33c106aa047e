#!/usr/bin/env python3
"""
ÉTAPE 1 - Reddit Scraper
Collecte des données de harcèlement depuis Reddit
Stockage dans MongoDB : base 'harcelement', collection 'posts'
"""

import praw
from pymongo import MongoClient
from datetime import datetime, timezone
import time
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RedditHarcelementScraper:
    def __init__(self):
        """Initialise le scraper Reddit"""
        # Configuration Reddit API
        self.reddit = praw.Reddit(
            client_id="naoE5J20fwcnusZT_C0vkA",
            client_secret="2827ZUySYcMwUp0TfyA8JG5cTvI7cw",
            user_agent="harassment_scraper_v1.0_by_Fit-Juice-9025"
        )
        
        # Configuration MongoDB
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]
        
        # Subreddits ciblés pour le harcèlement
        self.subreddits_cibles = ["bullying", "TrueOffMyChest", "cyberbullying"]
        self.limite_posts = 50  # Posts par subreddit
        
        logger.info("Reddit Scraper initialisé")
        logger.info(f"Subreddits ciblés: {self.subreddits_cibles}")
    
    def extraire_donnees_post(self, post, subreddit_name):
        """Extrait les champs requis d'un post Reddit"""
        return {
            "titre": post.title,
            "contenu": post.selftext,
            "auteur": str(post.author) if post.author else "[deleted]",
            "date": datetime.fromtimestamp(post.created_utc, timezone.utc),
            "url": post.url,
            "source": f"r/{subreddit_name}",
            "plateforme": "reddit",
            "post_id": post.id,
            "score": post.score,
            "nb_commentaires": post.num_comments,
            "date_collecte": datetime.now(timezone.utc)
        }
    
    def scraper_subreddit(self, subreddit_name):
        """Scrape un subreddit spécifique"""
        logger.info(f"Début scraping r/{subreddit_name}")
        posts_ajoutes = 0
        
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            
            for post in subreddit.hot(limit=self.limite_posts):
                if post.stickied:
                    continue  # Ignorer les posts épinglés
                
                # Extraire les données
                donnees_post = self.extraire_donnees_post(post, subreddit_name)
                
                # Vérifier si le post existe déjà (éviter doublons)
                if self.collection.count_documents({"url": post.url}) == 0:
                    self.collection.insert_one(donnees_post)
                    posts_ajoutes += 1
                    logger.info(f"[+] Post ajouté: {post.title[:60]}...")
                else:
                    logger.debug(f"[=] Post existant: {post.title[:60]}...")
                    
        except Exception as e:
            logger.error(f"Erreur lors du scraping de r/{subreddit_name}: {e}")
        
        logger.info(f"r/{subreddit_name}: {posts_ajoutes} nouveaux posts")
        return posts_ajoutes
    
    def scraper_tous_subreddits(self):
        """Scrape tous les subreddits ciblés"""
        logger.info("=" * 60)
        logger.info("DÉBUT COLLECTE REDDIT - ÉTAPE 1")
        logger.info("=" * 60)
        
        debut = time.time()
        total_posts = 0
        resultats = {}
        
        for subreddit_name in self.subreddits_cibles:
            posts_ajoutes = self.scraper_subreddit(subreddit_name)
            resultats[f"r/{subreddit_name}"] = posts_ajoutes
            total_posts += posts_ajoutes
            
            # Pause entre subreddits pour respecter les limites
            time.sleep(2)
        
        fin = time.time()
        duree = fin - debut
        
        # Résumé
        logger.info("=" * 60)
        logger.info("RÉSUMÉ COLLECTE REDDIT")
        logger.info("=" * 60)
        logger.info(f"Durée totale: {duree:.2f} secondes")
        logger.info(f"Total nouveaux posts: {total_posts}")
        
        for source, count in resultats.items():
            logger.info(f"  {source}: {count} posts")
        
        # Statistiques base de données
        total_db = self.collection.count_documents({})
        logger.info(f"Total posts en base: {total_db}")
        
        return resultats
    
    def obtenir_statistiques(self):
        """Obtient les statistiques de la collection"""
        logger.info("\nSTATISTIQUES BASE DE DONNÉES")
        logger.info("-" * 30)
        
        # Total documents
        total = self.collection.count_documents({})
        logger.info(f"Total documents: {total}")
        
        # Par plateforme
        pipeline_plateforme = [
            {"$group": {"_id": "$plateforme", "count": {"$sum": 1}}}
        ]
        for result in self.collection.aggregate(pipeline_plateforme):
            logger.info(f"Plateforme {result['_id']}: {result['count']} documents")
        
        # Par source
        pipeline_source = [
            {"$group": {"_id": "$source", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        logger.info("\nTop sources:")
        for result in self.collection.aggregate(pipeline_source):
            logger.info(f"  {result['_id']}: {result['count']} documents")
        
        return total
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("Connexions fermées")

def main():
    """Fonction principale"""
    scraper = None
    
    try:
        # Initialiser le scraper
        scraper = RedditHarcelementScraper()
        
        # Lancer la collecte
        resultats = scraper.scraper_tous_subreddits()
        
        # Afficher les statistiques
        scraper.obtenir_statistiques()
        
        logger.info("✅ COLLECTE REDDIT TERMINÉE AVEC SUCCÈS")
        
    except KeyboardInterrupt:
        logger.info("Collecte interrompue par l'utilisateur")
    except Exception as e:
        logger.error(f"Erreur: {e}")
    finally:
        if scraper:
            scraper.fermer_connexions()

if __name__ == "__main__":
    main()
