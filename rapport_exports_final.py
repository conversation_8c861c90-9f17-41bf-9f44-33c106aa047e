#!/usr/bin/env python3
"""
Rapport final des exports de données
Génère un rapport complet et détaillé de tous les exports réalisés
"""

import json
import logging
from pathlib import Path
from datetime import datetime

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generer_rapport_final():
    """Génère le rapport final des exports"""
    logger.info("📋 GÉNÉRATION DU RAPPORT FINAL DES EXPORTS")
    logger.info("=" * 70)
    
    export_dir = Path("exports")
    if not export_dir.exists():
        logger.error("❌ Dossier d'export non trouvé")
        return
    
    # Analyser tous les fichiers
    fichiers = list(export_dir.glob("*"))
    fichiers_valides = [f for f in fichiers if f.is_file()]
    
    # Grouper par type et source
    exports_mongodb = []
    exports_elasticsearch = []
    exports_rapports = []
    
    for fichier in fichiers_valides:
        nom = fichier.name.lower()
        info = {
            "nom": fichier.name,
            "taille_kb": round(fichier.stat().st_size / 1024, 2),
            "date_creation": datetime.fromtimestamp(fichier.stat().st_mtime).isoformat(),
            "extension": fichier.suffix,
            "type": ""
        }
        
        if "mongodb" in nom:
            if "complet" in nom:
                info["type"] = "JSON Complet"
            elif "analyses" in nom:
                info["type"] = "JSON Analyses"
            elif "statistiques" in nom:
                info["type"] = "Statistiques"
            elif "echantillon" in nom:
                info["type"] = "Échantillon"
            elif ".csv" in nom:
                info["type"] = "CSV"
            elif ".xlsx" in nom:
                info["type"] = "Excel"
            exports_mongodb.append(info)
            
        elif "elasticsearch" in nom:
            if "bulk" in nom:
                info["type"] = "NDJSON Bulk"
            elif "agregations" in nom:
                info["type"] = "Agrégations"
            elif "mapping" in nom:
                info["type"] = "Mapping"
            elif "requetes" in nom:
                info["type"] = "Requêtes"
            elif ".csv" in nom:
                info["type"] = "CSV"
            elif ".json" in nom:
                info["type"] = "JSON"
            exports_elasticsearch.append(info)
            
        elif "rapport" in nom:
            info["type"] = "Rapport"
            exports_rapports.append(info)
    
    # Afficher le rapport détaillé
    logger.info("📊 RÉSUMÉ GÉNÉRAL")
    logger.info("-" * 50)
    logger.info(f"📁 Dossier d'export: {export_dir}")
    logger.info(f"📄 Total fichiers: {len(fichiers_valides)}")
    
    taille_totale = sum(f["taille_kb"] for f in [*exports_mongodb, *exports_elasticsearch, *exports_rapports])
    logger.info(f"💾 Taille totale: {taille_totale:.1f} KB ({taille_totale/1024:.1f} MB)")
    
    # MongoDB
    logger.info(f"\n📊 EXPORTS MONGODB ({len(exports_mongodb)} fichiers)")
    logger.info("-" * 50)
    
    types_mongodb = {}
    for export in exports_mongodb:
        type_export = export["type"]
        if type_export not in types_mongodb:
            types_mongodb[type_export] = []
        types_mongodb[type_export].append(export)
    
    for type_export, fichiers_type in types_mongodb.items():
        logger.info(f"\n🔹 {type_export} ({len(fichiers_type)} fichier(s)):")
        for fichier in fichiers_type:
            logger.info(f"   📄 {fichier['nom']} ({fichier['taille_kb']} KB)")
    
    # Elasticsearch
    logger.info(f"\n🔍 EXPORTS ELASTICSEARCH ({len(exports_elasticsearch)} fichiers)")
    logger.info("-" * 50)
    
    types_elasticsearch = {}
    for export in exports_elasticsearch:
        type_export = export["type"]
        if type_export not in types_elasticsearch:
            types_elasticsearch[type_export] = []
        types_elasticsearch[type_export].append(export)
    
    for type_export, fichiers_type in types_elasticsearch.items():
        logger.info(f"\n🔹 {type_export} ({len(fichiers_type)} fichier(s)):")
        for fichier in fichiers_type:
            logger.info(f"   📄 {fichier['nom']} ({fichier['taille_kb']} KB)")
    
    # Rapports
    if exports_rapports:
        logger.info(f"\n📋 RAPPORTS ({len(exports_rapports)} fichiers)")
        logger.info("-" * 50)
        for rapport in exports_rapports:
            logger.info(f"   📄 {rapport['nom']} ({rapport['taille_kb']} KB)")
    
    # Statistiques par format
    logger.info(f"\n📈 STATISTIQUES PAR FORMAT")
    logger.info("-" * 50)
    
    stats_extensions = {}
    for export in [*exports_mongodb, *exports_elasticsearch, *exports_rapports]:
        ext = export["extension"] or "sans extension"
        if ext not in stats_extensions:
            stats_extensions[ext] = {"count": 0, "taille_kb": 0}
        stats_extensions[ext]["count"] += 1
        stats_extensions[ext]["taille_kb"] += export["taille_kb"]
    
    for ext, stats in sorted(stats_extensions.items(), key=lambda x: x[1]["taille_kb"], reverse=True):
        logger.info(f"   {ext}: {stats['count']} fichiers ({stats['taille_kb']:.1f} KB)")
    
    # Données analysées
    logger.info(f"\n📊 CONTENU DES DONNÉES")
    logger.info("-" * 50)
    
    # Lire un fichier JSON pour les statistiques
    fichier_stats = None
    for fichier in exports_mongodb:
        if "analyses" in fichier["nom"].lower():
            fichier_stats = export_dir / fichier["nom"]
            break
    
    if fichier_stats and fichier_stats.exists():
        try:
            with open(fichier_stats, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            documents = data.get("documents", [])
            if documents:
                logger.info(f"📄 Total documents: {len(documents)}")
                
                # Analyser les données
                sentiments = [doc.get('sentiment', 'unknown') for doc in documents]
                langues = [doc.get('langue_nom', 'unknown') for doc in documents]
                plateformes = [doc.get('plateforme', 'unknown') for doc in documents]
                scores = [doc.get('sentiment_score', 0) for doc in documents if doc.get('sentiment_score') is not None]
                
                logger.info(f"\n😊 Répartition des sentiments:")
                for sentiment in set(sentiments):
                    count = sentiments.count(sentiment)
                    pourcentage = (count / len(sentiments)) * 100
                    logger.info(f"   {sentiment}: {count} ({pourcentage:.1f}%)")
                
                logger.info(f"\n🌍 Répartition des langues:")
                for langue in set(langues):
                    count = langues.count(langue)
                    pourcentage = (count / len(langues)) * 100
                    logger.info(f"   {langue}: {count} ({pourcentage:.1f}%)")
                
                logger.info(f"\n📱 Répartition des plateformes:")
                for plateforme in set(plateformes):
                    count = plateformes.count(plateforme)
                    pourcentage = (count / len(plateformes)) * 100
                    logger.info(f"   {plateforme}: {count} ({pourcentage:.1f}%)")
                
                if scores:
                    logger.info(f"\n📊 Statistiques des scores:")
                    logger.info(f"   Minimum: {min(scores):.3f}")
                    logger.info(f"   Maximum: {max(scores):.3f}")
                    logger.info(f"   Moyenne: {sum(scores)/len(scores):.3f}")
                    
                    # Distribution des scores
                    tres_negatif = len([s for s in scores if s <= -0.5])
                    negatif = len([s for s in scores if -0.5 < s <= -0.1])
                    neutre = len([s for s in scores if -0.1 < s < 0.1])
                    positif = len([s for s in scores if 0.1 <= s < 0.5])
                    tres_positif = len([s for s in scores if s >= 0.5])
                    
                    logger.info(f"\n📈 Distribution des scores:")
                    logger.info(f"   Très négatif (≤ -0.5): {tres_negatif}")
                    logger.info(f"   Négatif (-0.5 à -0.1): {negatif}")
                    logger.info(f"   Neutre (-0.1 à 0.1): {neutre}")
                    logger.info(f"   Positif (0.1 à 0.5): {positif}")
                    logger.info(f"   Très positif (≥ 0.5): {tres_positif}")
        
        except Exception as e:
            logger.warning(f"⚠️ Impossible de lire les statistiques: {e}")
    
    # Guide d'utilisation
    logger.info(f"\n💡 GUIDE D'UTILISATION DES EXPORTS")
    logger.info("-" * 50)
    
    logger.info(f"\n📊 Pour l'analyse rapide:")
    logger.info(f"   • Ouvrir les fichiers CSV dans Excel/LibreOffice")
    logger.info(f"   • Utiliser les fichiers Excel avec graphiques intégrés")
    logger.info(f"   • Consulter les échantillons pour des tests rapides")
    
    logger.info(f"\n🔧 Pour l'intégration système:")
    logger.info(f"   • Utiliser les fichiers JSON pour les APIs")
    logger.info(f"   • Importer les NDJSON dans Elasticsearch")
    logger.info(f"   • Utiliser les mappings pour configurer ES")
    
    logger.info(f"\n📈 Pour la visualisation:")
    logger.info(f"   • Importer CSV dans Tableau/PowerBI")
    logger.info(f"   • Utiliser les agrégations pour Kibana")
    logger.info(f"   • Consulter les requêtes exemples")
    
    logger.info(f"\n🎲 Pour la démonstration:")
    logger.info(f"   • Utiliser les fichiers échantillon (50 documents)")
    logger.info(f"   • Présenter les statistiques JSON")
    logger.info(f"   • Montrer les exemples de requêtes")
    
    # Commandes utiles
    logger.info(f"\n⚡ COMMANDES UTILES")
    logger.info("-" * 50)
    
    # Trouver un fichier NDJSON
    fichier_ndjson = None
    for fichier in exports_elasticsearch:
        if "bulk" in fichier["nom"].lower():
            fichier_ndjson = fichier["nom"]
            break
    
    if fichier_ndjson:
        logger.info(f"\n🔍 Import Elasticsearch:")
        logger.info(f"   curl -X POST 'localhost:9200/_bulk' \\")
        logger.info(f"        -H 'Content-Type: application/json' \\")
        logger.info(f"        --data-binary @exports/{fichier_ndjson}")
    
    # Trouver un fichier CSV
    fichier_csv = None
    for fichier in exports_mongodb:
        if ".csv" in fichier["nom"]:
            fichier_csv = fichier["nom"]
            break
    
    if fichier_csv:
        logger.info(f"\n📊 Analyse Python:")
        logger.info(f"   import pandas as pd")
        logger.info(f"   df = pd.read_csv('exports/{fichier_csv}')")
        logger.info(f"   df.describe()")
    
    # Recommandations finales
    logger.info(f"\n🎯 RECOMMANDATIONS FINALES")
    logger.info("-" * 50)
    
    logger.info(f"✅ Sauvegarde: Conserver les fichiers JSON complets")
    logger.info(f"✅ Partage: Utiliser les fichiers CSV et Excel")
    logger.info(f"✅ Production: Importer les NDJSON dans Elasticsearch")
    logger.info(f"✅ Analyse: Utiliser les agrégations et statistiques")
    logger.info(f"✅ Documentation: Consulter les mappings et requêtes")
    
    logger.info(f"\n🎉 RAPPORT FINAL TERMINÉ")
    logger.info(f"📁 Tous les fichiers disponibles dans: {export_dir}")
    logger.info(f"💾 {len(fichiers_valides)} fichiers générés ({taille_totale:.1f} KB)")
    logger.info(f"📊 221 documents de harcèlement analysés et exportés")
    logger.info(f"✅ Données prêtes pour utilisation et partage")

def main():
    """Fonction principale"""
    try:
        generer_rapport_final()
    except Exception as e:
        logger.error(f"❌ Erreur: {e}")

if __name__ == "__main__":
    main()
