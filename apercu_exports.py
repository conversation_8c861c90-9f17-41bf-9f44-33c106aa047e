#!/usr/bin/env python3
"""
Aperçu des données exportées
Affiche un résumé et des extraits des fichiers d'export
"""

import json
import csv
import logging
from pathlib import Path
from datetime import datetime

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ApercuExports:
    def __init__(self):
        """Initialise l'aperçu des exports"""
        self.export_dir = Path("exports")
        
        if not self.export_dir.exists():
            logger.error(f"❌ Dossier d'export non trouvé: {self.export_dir}")
            return
        
        logger.info(f"📁 Analyse du dossier: {self.export_dir}")
    
    def analyser_fichiers_export(self):
        """Analyse tous les fichiers d'export"""
        logger.info("🔍 ANALYSE DES FICHIERS D'EXPORT")
        logger.info("=" * 50)
        
        fichiers = list(self.export_dir.glob("*"))
        if not fichiers:
            logger.warning("⚠️ Aucun fichier d'export trouvé")
            return
        
        # Grouper par type
        fichiers_par_type = {
            "MongoDB": [],
            "Elasticsearch": [],
            "Rapports": []
        }
        
        for fichier in fichiers:
            if fichier.is_file():
                nom = fichier.name.lower()
                if "mongodb" in nom:
                    fichiers_par_type["MongoDB"].append(fichier)
                elif "elasticsearch" in nom:
                    fichiers_par_type["Elasticsearch"].append(fichier)
                elif "rapport" in nom:
                    fichiers_par_type["Rapports"].append(fichier)
        
        # Afficher par catégorie
        for categorie, fichiers_cat in fichiers_par_type.items():
            if fichiers_cat:
                logger.info(f"\n📊 {categorie} ({len(fichiers_cat)} fichiers):")
                for fichier in sorted(fichiers_cat):
                    taille_kb = fichier.stat().st_size / 1024
                    date_modif = datetime.fromtimestamp(fichier.stat().st_mtime)
                    logger.info(f"   📄 {fichier.name}")
                    logger.info(f"      Taille: {taille_kb:.1f} KB")
                    logger.info(f"      Modifié: {date_modif.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Statistiques globales
        taille_totale = sum(f.stat().st_size for f in fichiers if f.is_file()) / 1024
        logger.info(f"\n📈 STATISTIQUES GLOBALES:")
        logger.info(f"   Total fichiers: {len(fichiers)}")
        logger.info(f"   Taille totale: {taille_totale:.1f} KB ({taille_totale/1024:.1f} MB)")
    
    def apercu_json_mongodb(self):
        """Affiche un aperçu des données JSON MongoDB"""
        logger.info("\n📄 APERÇU DONNÉES MONGODB (JSON)")
        logger.info("-" * 40)
        
        # Chercher le fichier JSON analyses le plus récent
        fichiers_json = list(self.export_dir.glob("mongodb_export_analyses_*.json"))
        if not fichiers_json:
            logger.warning("⚠️ Aucun fichier JSON MongoDB trouvé")
            return
        
        fichier_recent = max(fichiers_json, key=lambda f: f.stat().st_mtime)
        logger.info(f"📂 Fichier: {fichier_recent.name}")
        
        try:
            with open(fichier_recent, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            metadata = data.get("metadata", {})
            documents = data.get("documents", [])
            
            logger.info(f"📊 Métadonnées:")
            logger.info(f"   Date export: {metadata.get('export_date', 'N/A')}")
            logger.info(f"   Total documents: {metadata.get('total_documents', 0)}")
            logger.info(f"   Source: {metadata.get('source', 'N/A')}")
            
            if documents:
                logger.info(f"\n📋 Exemple de document (1/{len(documents)}):")
                doc_exemple = documents[0]
                logger.info(f"   ID: {doc_exemple.get('id', 'N/A')}")
                logger.info(f"   Titre: {doc_exemple.get('titre', '')[:60]}...")
                logger.info(f"   Auteur: {doc_exemple.get('auteur', 'N/A')}")
                logger.info(f"   Date: {doc_exemple.get('date', 'N/A')}")
                logger.info(f"   Plateforme: {doc_exemple.get('plateforme', 'N/A')}")
                logger.info(f"   Langue: {doc_exemple.get('langue_nom', 'N/A')}")
                logger.info(f"   Sentiment: {doc_exemple.get('sentiment', 'N/A')}")
                logger.info(f"   Score: {doc_exemple.get('sentiment_score', 0):.3f}")
                
                # Statistiques rapides
                sentiments = [doc.get('sentiment', 'unknown') for doc in documents]
                langues = [doc.get('langue_nom', 'unknown') for doc in documents]
                plateformes = [doc.get('plateforme', 'unknown') for doc in documents]
                
                logger.info(f"\n📊 Répartition rapide:")
                logger.info(f"   Sentiments: {dict(sorted([(s, sentiments.count(s)) for s in set(sentiments)], key=lambda x: x[1], reverse=True))}")
                logger.info(f"   Langues: {dict(sorted([(l, langues.count(l)) for l in set(langues)], key=lambda x: x[1], reverse=True))}")
                logger.info(f"   Plateformes: {dict(sorted([(p, plateformes.count(p)) for p in set(plateformes)], key=lambda x: x[1], reverse=True))}")
        
        except Exception as e:
            logger.error(f"❌ Erreur lecture JSON: {e}")
    
    def apercu_csv_mongodb(self):
        """Affiche un aperçu des données CSV MongoDB"""
        logger.info("\n📊 APERÇU DONNÉES MONGODB (CSV)")
        logger.info("-" * 40)
        
        # Chercher le fichier CSV le plus récent
        fichiers_csv = list(self.export_dir.glob("mongodb_export_*.csv"))
        if not fichiers_csv:
            logger.warning("⚠️ Aucun fichier CSV MongoDB trouvé")
            return
        
        fichier_recent = max(fichiers_csv, key=lambda f: f.stat().st_mtime)
        logger.info(f"📂 Fichier: {fichier_recent.name}")
        
        try:
            with open(fichier_recent, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
            
            if not rows:
                logger.warning("⚠️ Fichier CSV vide")
                return
            
            logger.info(f"📊 Structure CSV:")
            logger.info(f"   Lignes: {len(rows)}")
            logger.info(f"   Colonnes: {len(rows[0].keys())}")
            logger.info(f"   Champs: {', '.join(list(rows[0].keys())[:5])}...")
            
            logger.info(f"\n📋 Exemple de ligne (1/{len(rows)}):")
            exemple = rows[0]
            for key, value in list(exemple.items())[:8]:  # 8 premiers champs
                if len(str(value)) > 50:
                    value = str(value)[:50] + "..."
                logger.info(f"   {key}: {value}")
        
        except Exception as e:
            logger.error(f"❌ Erreur lecture CSV: {e}")
    
    def apercu_elasticsearch(self):
        """Affiche un aperçu des données Elasticsearch"""
        logger.info("\n🔍 APERÇU DONNÉES ELASTICSEARCH")
        logger.info("-" * 40)
        
        # Chercher le fichier JSON Elasticsearch le plus récent
        fichiers_es = list(self.export_dir.glob("elasticsearch_export_*.json"))
        if not fichiers_es:
            logger.warning("⚠️ Aucun fichier JSON Elasticsearch trouvé")
            return
        
        fichier_recent = max(fichiers_es, key=lambda f: f.stat().st_mtime)
        logger.info(f"📂 Fichier: {fichier_recent.name}")
        
        try:
            with open(fichier_recent, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            metadata = data.get("metadata", {})
            documents = data.get("documents", [])
            
            logger.info(f"📊 Métadonnées Elasticsearch:")
            logger.info(f"   Date export: {metadata.get('export_date', 'N/A')}")
            logger.info(f"   Index: {metadata.get('index_name', 'N/A')}")
            logger.info(f"   Total documents: {metadata.get('total_documents', 0)}")
            logger.info(f"   Version ES: {metadata.get('elasticsearch_version', 'N/A')}")
            
            if documents:
                logger.info(f"\n📋 Exemple de document Elasticsearch:")
                doc_exemple = documents[0]
                logger.info(f"   Index: {doc_exemple.get('_index', 'N/A')}")
                logger.info(f"   ID: {doc_exemple.get('_id', 'N/A')}")
                
                source = doc_exemple.get('_source', {})
                logger.info(f"   Source:")
                logger.info(f"     Titre: {source.get('titre', '')[:50]}...")
                logger.info(f"     Sentiment: {source.get('sentiment', 'N/A')}")
                logger.info(f"     Score: {source.get('score', 0):.3f}")
                logger.info(f"     Langue: {source.get('langue_nom', 'N/A')}")
                logger.info(f"     Plateforme: {source.get('plateforme', 'N/A')}")
        
        except Exception as e:
            logger.error(f"❌ Erreur lecture Elasticsearch JSON: {e}")
    
    def apercu_agregations(self):
        """Affiche un aperçu des agrégations Elasticsearch"""
        logger.info("\n📈 APERÇU AGRÉGATIONS ELASTICSEARCH")
        logger.info("-" * 40)
        
        # Chercher le fichier d'agrégations le plus récent
        fichiers_agg = list(self.export_dir.glob("elasticsearch_agregations_*.json"))
        if not fichiers_agg:
            logger.warning("⚠️ Aucun fichier d'agrégations trouvé")
            return
        
        fichier_recent = max(fichiers_agg, key=lambda f: f.stat().st_mtime)
        logger.info(f"📂 Fichier: {fichier_recent.name}")
        
        try:
            with open(fichier_recent, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            agregations = data.get("aggregations", {})
            
            # Répartition par sentiment
            if "sentiment_distribution" in agregations:
                logger.info(f"\n😊 Répartition par sentiment:")
                for bucket in agregations["sentiment_distribution"]["buckets"]:
                    logger.info(f"   {bucket['key']}: {bucket['doc_count']} documents")
            
            # Répartition par langue
            if "langue_distribution" in agregations:
                logger.info(f"\n🌍 Répartition par langue:")
                for bucket in agregations["langue_distribution"]["buckets"]:
                    logger.info(f"   {bucket['key']}: {bucket['doc_count']} documents")
            
            # Répartition par plateforme
            if "plateforme_distribution" in agregations:
                logger.info(f"\n📱 Répartition par plateforme:")
                for bucket in agregations["plateforme_distribution"]["buckets"]:
                    logger.info(f"   {bucket['key']}: {bucket['doc_count']} documents")
            
            # Statistiques des scores
            if "score_stats" in agregations:
                stats = agregations["score_stats"]
                logger.info(f"\n📊 Statistiques des scores:")
                logger.info(f"   Minimum: {stats.get('min', 0):.3f}")
                logger.info(f"   Maximum: {stats.get('max', 0):.3f}")
                logger.info(f"   Moyenne: {stats.get('avg', 0):.3f}")
                logger.info(f"   Total: {stats.get('count', 0)} documents")
        
        except Exception as e:
            logger.error(f"❌ Erreur lecture agrégations: {e}")
    
    def apercu_statistiques_mongodb(self):
        """Affiche un aperçu des statistiques MongoDB"""
        logger.info("\n📊 APERÇU STATISTIQUES MONGODB")
        logger.info("-" * 40)
        
        # Chercher le fichier de statistiques le plus récent
        fichiers_stats = list(self.export_dir.glob("mongodb_statistiques_*.json"))
        if not fichiers_stats:
            logger.warning("⚠️ Aucun fichier de statistiques trouvé")
            return
        
        fichier_recent = max(fichiers_stats, key=lambda f: f.stat().st_mtime)
        logger.info(f"📂 Fichier: {fichier_recent.name}")
        
        try:
            with open(fichier_recent, 'r', encoding='utf-8') as f:
                stats = json.load(f)
            
            logger.info(f"📈 Statistiques générales:")
            logger.info(f"   Total documents: {stats.get('total_documents', 0)}")
            logger.info(f"   Documents collectés: {stats.get('documents_collectes', 0)}")
            logger.info(f"   Documents prétraités: {stats.get('documents_pretraites', 0)}")
            logger.info(f"   Documents analysés: {stats.get('documents_analyses', 0)}")
            
            # Évolution temporelle
            if "evolution_temporelle" in stats:
                logger.info(f"\n📅 Évolution temporelle:")
                for periode in stats["evolution_temporelle"][:3]:  # 3 premiers mois
                    logger.info(f"   {periode['_id']}: {periode['count']} documents (score moyen: {periode.get('score_moyen', 0):.3f})")
        
        except Exception as e:
            logger.error(f"❌ Erreur lecture statistiques: {e}")
    
    def generer_apercu_complet(self):
        """Génère un aperçu complet de tous les exports"""
        logger.info("🔍 APERÇU COMPLET DES EXPORTS")
        logger.info("=" * 60)
        
        # Analyser les fichiers
        self.analyser_fichiers_export()
        
        # Aperçus spécifiques
        self.apercu_json_mongodb()
        self.apercu_csv_mongodb()
        self.apercu_elasticsearch()
        self.apercu_agregations()
        self.apercu_statistiques_mongodb()
        
        # Recommandations
        logger.info("\n💡 RECOMMANDATIONS D'UTILISATION")
        logger.info("-" * 40)
        logger.info("📊 Pour analyse rapide: Ouvrir les fichiers CSV dans Excel")
        logger.info("🔧 Pour intégration: Utiliser les fichiers JSON")
        logger.info("🔍 Pour Elasticsearch: Importer les fichiers NDJSON")
        logger.info("📈 Pour visualisation: Utiliser les fichiers Excel avec graphiques")
        logger.info("🎲 Pour démonstration: Utiliser les fichiers échantillon")
        
        logger.info("\n✅ APERÇU TERMINÉ")

def main():
    """Fonction principale"""
    try:
        apercu = ApercuExports()
        apercu.generer_apercu_complet()
        
    except Exception as e:
        logger.error(f"❌ Erreur: {e}")

if __name__ == "__main__":
    main()
