#!/usr/bin/env python3
"""
ÉTAPE 5 - Visualisation avec Kibana
Crée un tableau de bord Kibana pour analyser les données de harcèlement
Génère les configurations JSON pour les visualisations et le dashboard
"""

import json
import logging
from datetime import datetime
from pymongo import MongoClient

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KibanaDashboardGenerator:
    def __init__(self):
        """Initialise le générateur de dashboard Kibana"""
        # Configuration MongoDB pour analyser les données
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]
        
        # Configuration Kibana
        self.index_pattern = "harcelement_posts"
        self.dashboard_id = "harcelement-analysis-dashboard"
        
        logger.info("Générateur de dashboard Kibana initialisé")
        logger.info(f"Index pattern: {self.index_pattern}")
    
    def analyser_donnees_mongodb(self):
        """Analyse les données MongoDB pour optimiser les visualisations"""
        total_docs = self.collection.count_documents({"nlp_effectue": True})
        
        # Analyser les langues
        langues = self.collection.distinct("langue_nom", {"nlp_effectue": True})
        
        # Analyser les sentiments
        sentiments = self.collection.distinct("sentiment", {"nlp_effectue": True})
        
        # Analyser les plateformes
        plateformes = self.collection.distinct("plateforme", {"nlp_effectue": True})
        
        # Analyser les dates (plage temporelle)
        pipeline_dates = [
            {"$match": {"nlp_effectue": True, "date": {"$exists": True}}},
            {"$group": {
                "_id": None,
                "date_min": {"$min": "$date"},
                "date_max": {"$max": "$date"}
            }}
        ]
        
        dates_result = list(self.collection.aggregate(pipeline_dates))
        date_min = dates_result[0]["date_min"] if dates_result else None
        date_max = dates_result[0]["date_max"] if dates_result else None
        
        logger.info(f"📊 Analyse des données:")
        logger.info(f"  Total documents: {total_docs}")
        logger.info(f"  Langues: {langues}")
        logger.info(f"  Sentiments: {sentiments}")
        logger.info(f"  Plateformes: {plateformes}")
        logger.info(f"  Période: {date_min} → {date_max}")
        
        return {
            "total_docs": total_docs,
            "langues": langues,
            "sentiments": sentiments,
            "plateformes": plateformes,
            "date_min": date_min,
            "date_max": date_max
        }
    
    def generer_index_pattern(self):
        """Génère la configuration de l'index pattern Kibana"""
        index_pattern_config = {
            "version": "8.0.0",
            "objects": [
                {
                    "id": self.index_pattern,
                    "type": "index-pattern",
                    "attributes": {
                        "title": self.index_pattern,
                        "timeFieldName": "date",
                        "fields": json.dumps([
                            {"name": "titre", "type": "string", "searchable": True, "aggregatable": False},
                            {"name": "contenu", "type": "string", "searchable": True, "aggregatable": False},
                            {"name": "auteur", "type": "string", "searchable": True, "aggregatable": True},
                            {"name": "date", "type": "date", "searchable": True, "aggregatable": True},
                            {"name": "url", "type": "string", "searchable": False, "aggregatable": True},
                            {"name": "langue", "type": "string", "searchable": True, "aggregatable": True},
                            {"name": "langue_nom", "type": "string", "searchable": True, "aggregatable": True},
                            {"name": "sentiment", "type": "string", "searchable": True, "aggregatable": True},
                            {"name": "score", "type": "number", "searchable": True, "aggregatable": True},
                            {"name": "plateforme", "type": "string", "searchable": True, "aggregatable": True},
                            {"name": "source", "type": "string", "searchable": True, "aggregatable": True}
                        ])
                    }
                }
            ]
        }
        
        return index_pattern_config
    
    def generer_visualisation_langues(self):
        """Génère la visualisation de répartition des langues"""
        viz_langues = {
            "id": "langues-repartition",
            "type": "visualization",
            "attributes": {
                "title": "Répartition des Langues",
                "visState": json.dumps({
                    "title": "Répartition des Langues",
                    "type": "pie",
                    "aggs": [
                        {
                            "id": "1",
                            "type": "count",
                            "schema": "metric",
                            "params": {}
                        },
                        {
                            "id": "2",
                            "type": "terms",
                            "schema": "segment",
                            "params": {
                                "field": "langue_nom",
                                "size": 10,
                                "order": "desc",
                                "orderBy": "1"
                            }
                        }
                    ]
                }),
                "uiStateJSON": "{}",
                "description": "Répartition des documents par langue détectée",
                "kibanaSavedObjectMeta": {
                    "searchSourceJSON": json.dumps({
                        "index": self.index_pattern,
                        "query": {"match_all": {}},
                        "filter": []
                    })
                }
            }
        }
        
        return viz_langues
    
    def generer_visualisation_sentiments(self):
        """Génère la visualisation de répartition des sentiments"""
        viz_sentiments = {
            "id": "sentiments-repartition",
            "type": "visualization",
            "attributes": {
                "title": "Répartition des Sentiments",
                "visState": json.dumps({
                    "title": "Répartition des Sentiments",
                    "type": "histogram",
                    "aggs": [
                        {
                            "id": "1",
                            "type": "count",
                            "schema": "metric",
                            "params": {}
                        },
                        {
                            "id": "2",
                            "type": "terms",
                            "schema": "segment",
                            "params": {
                                "field": "sentiment",
                                "size": 5,
                                "order": "desc",
                                "orderBy": "1"
                            }
                        }
                    ]
                }),
                "uiStateJSON": json.dumps({
                    "vis": {
                        "colors": {
                            "négatif": "#E74C3C",
                            "neutre": "#F39C12",
                            "positif": "#27AE60"
                        }
                    }
                }),
                "description": "Répartition des documents par sentiment (positif, neutre, négatif)",
                "kibanaSavedObjectMeta": {
                    "searchSourceJSON": json.dumps({
                        "index": self.index_pattern,
                        "query": {"match_all": {}},
                        "filter": []
                    })
                }
            }
        }
        
        return viz_sentiments
    
    def generer_visualisation_evolution_temporelle(self):
        """Génère la visualisation d'évolution temporelle"""
        viz_temporelle = {
            "id": "evolution-temporelle",
            "type": "visualization",
            "attributes": {
                "title": "Évolution Temporelle des Publications",
                "visState": json.dumps({
                    "title": "Évolution Temporelle des Publications",
                    "type": "line",
                    "aggs": [
                        {
                            "id": "1",
                            "type": "count",
                            "schema": "metric",
                            "params": {}
                        },
                        {
                            "id": "2",
                            "type": "date_histogram",
                            "schema": "segment",
                            "params": {
                                "field": "date",
                                "interval": "auto",
                                "min_doc_count": 1
                            }
                        },
                        {
                            "id": "3",
                            "type": "terms",
                            "schema": "group",
                            "params": {
                                "field": "sentiment",
                                "size": 3,
                                "order": "desc",
                                "orderBy": "1"
                            }
                        }
                    ]
                }),
                "uiStateJSON": "{}",
                "description": "Évolution du nombre de publications dans le temps, segmentée par sentiment",
                "kibanaSavedObjectMeta": {
                    "searchSourceJSON": json.dumps({
                        "index": self.index_pattern,
                        "query": {"match_all": {}},
                        "filter": []
                    })
                }
            }
        }
        
        return viz_temporelle
    
    def generer_visualisation_contenus_negatifs(self):
        """Génère la liste des contenus les plus négatifs"""
        viz_negatifs = {
            "id": "contenus-negatifs",
            "type": "search",
            "attributes": {
                "title": "Contenus les Plus Négatifs",
                "description": "Liste des contenus avec les scores de sentiment les plus négatifs",
                "hits": 0,
                "columns": ["titre", "auteur", "score", "plateforme", "langue_nom"],
                "sort": ["score", "desc"],
                "kibanaSavedObjectMeta": {
                    "searchSourceJSON": json.dumps({
                        "index": self.index_pattern,
                        "query": {
                            "bool": {
                                "must": [
                                    {"term": {"sentiment": "négatif"}},
                                    {"range": {"score": {"lt": -0.5}}}
                                ]
                            }
                        },
                        "filter": [],
                        "sort": [{"score": {"order": "asc"}}]
                    })
                }
            }
        }
        
        return viz_negatifs
    
    def generer_visualisation_plateformes(self):
        """Génère la visualisation par plateforme"""
        viz_plateformes = {
            "id": "repartition-plateformes",
            "type": "visualization",
            "attributes": {
                "title": "Répartition par Plateforme",
                "visState": json.dumps({
                    "title": "Répartition par Plateforme",
                    "type": "pie",
                    "aggs": [
                        {
                            "id": "1",
                            "type": "count",
                            "schema": "metric",
                            "params": {}
                        },
                        {
                            "id": "2",
                            "type": "terms",
                            "schema": "segment",
                            "params": {
                                "field": "plateforme",
                                "size": 10,
                                "order": "desc",
                                "orderBy": "1"
                            }
                        }
                    ]
                }),
                "uiStateJSON": json.dumps({
                    "vis": {
                        "colors": {
                            "reddit": "#FF4500",
                            "twitter": "#1DA1F2",
                            "telegram": "#0088CC"
                        }
                    }
                }),
                "description": "Répartition des documents par plateforme source",
                "kibanaSavedObjectMeta": {
                    "searchSourceJSON": json.dumps({
                        "index": self.index_pattern,
                        "query": {"match_all": {}},
                        "filter": []
                    })
                }
            }
        }
        
        return viz_plateformes
    
    def generer_dashboard_principal(self):
        """Génère le dashboard principal avec toutes les visualisations"""
        dashboard = {
            "id": self.dashboard_id,
            "type": "dashboard",
            "attributes": {
                "title": "Analyse du Harcèlement en Ligne",
                "description": "Tableau de bord complet pour l'analyse des données de harcèlement collectées sur les réseaux sociaux",
                "panelsJSON": json.dumps([
                    {
                        "id": "langues-repartition",
                        "type": "visualization",
                        "gridData": {"x": 0, "y": 0, "w": 24, "h": 15}
                    },
                    {
                        "id": "sentiments-repartition",
                        "type": "visualization",
                        "gridData": {"x": 24, "y": 0, "w": 24, "h": 15}
                    },
                    {
                        "id": "repartition-plateformes",
                        "type": "visualization",
                        "gridData": {"x": 0, "y": 15, "w": 24, "h": 15}
                    },
                    {
                        "id": "evolution-temporelle",
                        "type": "visualization",
                        "gridData": {"x": 0, "y": 30, "w": 48, "h": 20}
                    },
                    {
                        "id": "contenus-negatifs",
                        "type": "search",
                        "gridData": {"x": 0, "y": 50, "w": 48, "h": 25}
                    }
                ]),
                "optionsJSON": json.dumps({
                    "useMargins": True,
                    "syncColors": False,
                    "hidePanelTitles": False
                }),
                "timeRestore": True,
                "timeTo": "now",
                "timeFrom": "now-30d",
                "refreshInterval": {
                    "pause": False,
                    "value": 300000
                },
                "kibanaSavedObjectMeta": {
                    "searchSourceJSON": json.dumps({
                        "query": {"match_all": {}},
                        "filter": []
                    })
                }
            }
        }
        
        return dashboard
    
    def generer_configuration_complete(self):
        """Génère la configuration complète pour Kibana"""
        logger.info("=" * 60)
        logger.info("GÉNÉRATION DASHBOARD KIBANA - ÉTAPE 5")
        logger.info("=" * 60)
        
        # Analyser les données
        stats = self.analyser_donnees_mongodb()
        
        # Générer toutes les configurations
        config_complete = {
            "version": "8.0.0",
            "objects": []
        }
        
        # Index pattern
        index_pattern = self.generer_index_pattern()
        config_complete["objects"].extend(index_pattern["objects"])
        
        # Visualisations
        visualisations = [
            self.generer_visualisation_langues(),
            self.generer_visualisation_sentiments(),
            self.generer_visualisation_evolution_temporelle(),
            self.generer_visualisation_contenus_negatifs(),
            self.generer_visualisation_plateformes()
        ]
        
        config_complete["objects"].extend(visualisations)
        
        # Dashboard principal
        dashboard = self.generer_dashboard_principal()
        config_complete["objects"].append(dashboard)
        
        return config_complete, stats
    
    def sauvegarder_configuration(self, config, nom_fichier="kibana_dashboard_config.json"):
        """Sauvegarde la configuration dans un fichier JSON"""
        try:
            with open(nom_fichier, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"✅ Configuration sauvegardée: {nom_fichier}")
            return True
        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde: {e}")
            return False
    
    def generer_instructions_import(self):
        """Génère les instructions pour importer dans Kibana"""
        instructions = """
# INSTRUCTIONS D'IMPORT DANS KIBANA

## 1. Prérequis
- Elasticsearch et Kibana démarrés
- Index 'harcelement_posts' créé dans Elasticsearch
- Données indexées depuis l'Étape 4

## 2. Import de la configuration
1. Ouvrir Kibana (http://localhost:5601)
2. Aller dans "Stack Management" > "Saved Objects"
3. Cliquer sur "Import"
4. Sélectionner le fichier 'kibana_dashboard_config.json'
5. Cliquer sur "Import"

## 3. Accès au dashboard
1. Aller dans "Analytics" > "Dashboard"
2. Ouvrir "Analyse du Harcèlement en Ligne"
3. Configurer la période temporelle si nécessaire

## 4. Filtres interactifs disponibles
- Langue (langue_nom)
- Sentiment (sentiment)
- Score de sentiment (score)
- Date (date)
- Plateforme (plateforme)
- Source (source)

## 5. Visualisations incluses
- Répartition des langues (camembert)
- Répartition des sentiments (histogramme coloré)
- Répartition par plateforme (camembert)
- Évolution temporelle (graphique linéaire)
- Contenus les plus négatifs (tableau)

## 6. Personnalisation
- Modifier les couleurs dans les options de visualisation
- Ajuster les périodes temporelles
- Ajouter des filtres personnalisés
- Créer de nouvelles visualisations
        """
        
        return instructions
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("Connexions fermées")

def main():
    """Fonction principale"""
    generator = None
    
    try:
        # Initialiser le générateur
        generator = KibanaDashboardGenerator()
        
        # Générer la configuration complète
        config, stats = generator.generer_configuration_complete()
        
        # Sauvegarder la configuration
        if generator.sauvegarder_configuration(config):
            
            # Générer et sauvegarder les instructions
            instructions = generator.generer_instructions_import()
            with open("kibana_import_instructions.txt", 'w', encoding='utf-8') as f:
                f.write(instructions)
            
            logger.info("=" * 60)
            logger.info("RÉSUMÉ DASHBOARD KIBANA")
            logger.info("=" * 60)
            logger.info(f"📊 Données analysées: {stats['total_docs']} documents")
            logger.info(f"🌍 Langues: {len(stats['langues'])} ({', '.join(stats['langues'])})")
            logger.info(f"😊 Sentiments: {len(stats['sentiments'])} ({', '.join(stats['sentiments'])})")
            logger.info(f"📱 Plateformes: {len(stats['plateformes'])} ({', '.join(stats['plateformes'])})")
            
            logger.info(f"\n📈 Visualisations créées:")
            logger.info(f"  ✅ Répartition des langues")
            logger.info(f"  ✅ Répartition des sentiments")
            logger.info(f"  ✅ Répartition par plateforme")
            logger.info(f"  ✅ Évolution temporelle des publications")
            logger.info(f"  ✅ Liste des contenus les plus négatifs")
            
            logger.info(f"\n🎛️ Filtres interactifs:")
            logger.info(f"  ✅ Langue (langue_nom)")
            logger.info(f"  ✅ Sentiment (sentiment)")
            logger.info(f"  ✅ Score de sentiment (score)")
            logger.info(f"  ✅ Date (date)")
            logger.info(f"  ✅ Plateforme (plateforme)")
            logger.info(f"  ✅ Source (source)")
            
            logger.info(f"\n📁 Fichiers générés:")
            logger.info(f"  ✅ kibana_dashboard_config.json")
            logger.info(f"  ✅ kibana_import_instructions.txt")
            
            logger.info("\n✅ ÉTAPE 5 TERMINÉE AVEC SUCCÈS !")
            logger.info("✅ Dashboard Kibana configuré")
            logger.info("✅ Toutes les visualisations créées")
            logger.info("✅ Filtres interactifs configurés")
            logger.info("✅ Prêt pour import dans Kibana")
        else:
            logger.error("❌ Échec de la sauvegarde")
        
    except KeyboardInterrupt:
        logger.info("Génération interrompue par l'utilisateur")
    except Exception as e:
        logger.error(f"Erreur: {e}")
    finally:
        if generator:
            generator.fermer_connexions()

if __name__ == "__main__":
    main()
