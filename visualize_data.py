#!/usr/bin/env python3
"""
Simple data visualization for harassment data collection
Creates basic charts and statistics
"""

import logging
from datetime import datetime, timedelta
from collections import Counter
import json
from config import Config
from database import DatabaseManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class DataVisualizer:
    """Simple data visualizer using text-based charts"""
    
    def __init__(self):
        self.config = Config()
        self.db_manager = DatabaseManager(self.config)
    
    def create_text_bar_chart(self, data, title, max_width=50):
        """Create a simple text-based bar chart"""
        if not data:
            return
        
        print(f"\n{title}")
        print("=" * len(title))
        
        # Find the maximum value for scaling
        max_value = max(data.values()) if data else 1
        
        for label, value in sorted(data.items(), key=lambda x: x[1], reverse=True):
            # Calculate bar length
            bar_length = int((value / max_value) * max_width)
            bar = "█" * bar_length
            
            # Format the output
            print(f"{label:<20} │{bar:<{max_width}} │ {value}")
    
    def visualize_platform_distribution(self):
        """Visualize posts by platform"""
        stats = self.db_manager.get_statistics()
        platforms = stats.get('platforms', {})
        
        # Filter out None values
        platforms = {k: v for k, v in platforms.items() if k}
        
        self.create_text_bar_chart(platforms, "📊 Posts by Platform")
    
    def visualize_source_distribution(self):
        """Visualize posts by source"""
        stats = self.db_manager.get_statistics()
        sources = stats.get('sources', {})
        
        # Take top 10 sources
        top_sources = dict(sorted(sources.items(), key=lambda x: x[1], reverse=True)[:10])
        
        self.create_text_bar_chart(top_sources, "📈 Top 10 Sources")
    
    def visualize_daily_activity(self, days=14):
        """Visualize daily posting activity"""
        recent_posts = self.db_manager.get_recent_posts(days=days, limit=1000)
        
        if not recent_posts:
            print("\n📅 No recent posts found")
            return
        
        # Group posts by date
        daily_counts = Counter()
        
        for post in recent_posts:
            post_date = post.get('date')
            if post_date:
                date_str = post_date.strftime('%Y-%m-%d')
                daily_counts[date_str] += 1
        
        # Fill in missing dates with 0
        start_date = datetime.now() - timedelta(days=days)
        for i in range(days):
            date_str = (start_date + timedelta(days=i)).strftime('%Y-%m-%d')
            if date_str not in daily_counts:
                daily_counts[date_str] = 0
        
        # Sort by date
        sorted_daily = dict(sorted(daily_counts.items()))
        
        self.create_text_bar_chart(sorted_daily, f"📅 Daily Activity (Last {days} days)")
    
    def visualize_keyword_frequency(self):
        """Visualize harassment keyword frequency"""
        posts = self.db_manager.get_recent_posts(days=30, limit=200)
        
        if not posts:
            print("\n🔍 No posts found for keyword analysis")
            return
        
        # Define harassment-related keywords
        harassment_keywords = [
            'bullying', 'harassment', 'cyberbullying', 'abuse', 'threats',
            'stalking', 'intimidation', 'trolling', 'hate', 'suicide',
            'depression', 'anxiety', 'mental health', 'help', 'scared'
        ]
        
        keyword_counts = Counter()
        
        for post in posts:
            content = (post.get('title', '') + ' ' + post.get('content', '')).lower()
            
            for keyword in harassment_keywords:
                if keyword in content:
                    keyword_counts[keyword] += 1
        
        # Take top 10 keywords
        top_keywords = dict(keyword_counts.most_common(10))
        
        self.create_text_bar_chart(top_keywords, "🔍 Top Harassment Keywords (Last 30 days)")
    
    def create_summary_table(self):
        """Create a summary statistics table"""
        stats = self.db_manager.get_statistics()
        recent_posts = self.db_manager.get_recent_posts(days=7, limit=1000)
        
        print("\n📋 SUMMARY STATISTICS")
        print("=" * 40)
        print(f"Total posts in database:     {stats.get('total_posts', 0):,}")
        print(f"Posts in last 7 days:       {len(recent_posts):,}")
        print(f"Number of platforms:         {len([k for k in stats.get('platforms', {}).keys() if k])}")
        print(f"Number of sources:           {len(stats.get('sources', {}))}")
        
        date_range = stats.get('date_range', {})
        if date_range:
            earliest = date_range.get('earliest')
            latest = date_range.get('latest')
            if earliest and latest:
                duration = latest - earliest
                print(f"Data collection period:      {duration.days} days")
                print(f"Earliest post:               {earliest.strftime('%Y-%m-%d %H:%M')}")
                print(f"Latest post:                 {latest.strftime('%Y-%m-%d %H:%M')}")
        
        # Calculate average posts per day
        if recent_posts:
            avg_per_day = len(recent_posts) / 7
            print(f"Average posts per day:       {avg_per_day:.1f}")
    
    def create_platform_breakdown(self):
        """Create detailed platform breakdown"""
        print("\n🔍 PLATFORM BREAKDOWN")
        print("=" * 30)
        
        platforms = self.db_manager.get_statistics().get('platforms', {})
        
        for platform, count in platforms.items():
            if not platform:
                continue
                
            print(f"\n{platform.upper()}:")
            print("-" * 15)
            
            # Get posts for this platform
            platform_posts = self.db_manager.get_posts_by_platform(platform, limit=100)
            
            if platform_posts:
                # Analyze sources within this platform
                source_counts = Counter(post.get('source') for post in platform_posts)
                
                print(f"Total posts: {count}")
                print("Top sources:")
                for source, source_count in source_counts.most_common(5):
                    percentage = (source_count / count) * 100
                    print(f"  {source}: {source_count} ({percentage:.1f}%)")
    
    def run_visualization(self):
        """Run complete visualization"""
        try:
            print("🎨 HARASSMENT DATA VISUALIZATION")
            print("=" * 50)
            
            self.create_summary_table()
            self.visualize_platform_distribution()
            self.visualize_source_distribution()
            self.visualize_daily_activity()
            self.visualize_keyword_frequency()
            self.create_platform_breakdown()
            
            print("\n" + "=" * 50)
            print("✅ Visualization completed!")
            print("=" * 50)
            
        except Exception as e:
            logger.error(f"Visualization failed: {e}")
        finally:
            self.db_manager.close_connection()

def main():
    """Main entry point"""
    visualizer = DataVisualizer()
    visualizer.run_visualization()

if __name__ == "__main__":
    main()
