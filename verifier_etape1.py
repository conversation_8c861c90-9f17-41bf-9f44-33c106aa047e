#!/usr/bin/env python3
"""
Vérification de l'Étape 1 : Collecte de données
Vérifie que les données ont été correctement collectées et stockées
"""

import logging
from datetime import datetime, timedelta
from config import Config
from database import DatabaseManager

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class VerificateurEtape1:
    """Vérificateur pour l'Étape 1"""
    
    def __init__(self):
        self.config = Config()
        self.db_manager = DatabaseManager(self.config)
    
    def verifier_structure_donnees(self):
        """Vérifie que les données ont la bonne structure"""
        logger.info("🔍 Vérification de la structure des données...")
        
        # Récupérer quelques documents
        posts = self.db_manager.get_recent_posts(days=30, limit=10)
        
        if not posts:
            logger.error("❌ Aucun document trouvé dans la base")
            return False
        
        # Vérifier les champs requis
        champs_requis = ["titre", "contenu", "auteur", "date", "url"]
        
        for i, post in enumerate(posts[:3]):
            logger.info(f"\n📄 Document {i+1}:")
            
            # Mapper les champs (notre structure utilise 'title' au lieu de 'titre', etc.)
            champs_mapping = {
                "titre": "title",
                "contenu": "content", 
                "auteur": "author",
                "date": "date",
                "url": "url"
            }
            
            for champ_fr, champ_en in champs_mapping.items():
                if champ_en in post:
                    valeur = post[champ_en]
                    if isinstance(valeur, str) and len(valeur) > 50:
                        valeur = valeur[:50] + "..."
                    logger.info(f"  ✅ {champ_fr}: {valeur}")
                else:
                    logger.warning(f"  ⚠️ {champ_fr} manquant")
            
            logger.info(f"  📱 Plateforme: {post.get('platform', 'Non spécifié')}")
            logger.info(f"  📍 Source: {post.get('source', 'Non spécifié')}")
        
        return True
    
    def verifier_plateformes(self):
        """Vérifie les données par plateforme"""
        logger.info("\n📊 Vérification par plateforme...")
        
        stats = self.db_manager.get_statistics()
        plateformes = stats.get('platforms', {})
        
        if not plateformes:
            logger.warning("⚠️ Aucune plateforme trouvée")
            return
        
        for plateforme, count in plateformes.items():
            if plateforme:
                logger.info(f"  📱 {plateforme.upper()}: {count} documents")
                
                # Vérifier quelques posts de cette plateforme
                posts_plateforme = self.db_manager.get_posts_by_platform(plateforme, limit=3)
                for post in posts_plateforme:
                    titre = post.get('title', 'Sans titre')[:40] + "..."
                    source = post.get('source', 'Source inconnue')
                    logger.info(f"    - {titre} (Source: {source})")
    
    def verifier_sources(self):
        """Vérifie les sources de données"""
        logger.info("\n📍 Vérification des sources...")
        
        stats = self.db_manager.get_statistics()
        sources = stats.get('sources', {})
        
        # Sources attendues pour l'étape 1
        sources_attendues = [
            'r/bullying', 'r/TrueOffMyChest', 'r/cyberbullying',
            'Twitter:', 'Telegram:'
        ]
        
        sources_trouvees = []
        for source in sources.keys():
            for source_attendue in sources_attendues:
                if source_attendue in source:
                    sources_trouvees.append(source_attendue)
                    break
        
        logger.info(f"Sources trouvées: {len(sources)} total")
        for source, count in sorted(sources.items(), key=lambda x: x[1], reverse=True)[:10]:
            logger.info(f"  📍 {source}: {count} documents")
        
        # Vérifier les sources Reddit spécifiquement
        sources_reddit = [s for s in sources.keys() if s.startswith('r/')]
        if sources_reddit:
            logger.info(f"\n✅ Sources Reddit trouvées: {len(sources_reddit)}")
            for source in sources_reddit:
                logger.info(f"  - {source}: {sources[source]} posts")
        else:
            logger.warning("⚠️ Aucune source Reddit trouvée")
    
    def verifier_donnees_recentes(self):
        """Vérifie les données collectées récemment"""
        logger.info("\n📅 Vérification des données récentes...")
        
        # Données des 7 derniers jours
        posts_recents = self.db_manager.get_recent_posts(days=7, limit=100)
        
        if posts_recents:
            logger.info(f"✅ {len(posts_recents)} documents des 7 derniers jours")
            
            # Grouper par jour
            from collections import Counter
            dates = []
            for post in posts_recents:
                if post.get('date'):
                    date_str = post['date'].strftime('%Y-%m-%d')
                    dates.append(date_str)
            
            compteur_dates = Counter(dates)
            logger.info("Répartition par jour:")
            for date, count in sorted(compteur_dates.items(), reverse=True)[:7]:
                logger.info(f"  📅 {date}: {count} documents")
        else:
            logger.warning("⚠️ Aucun document récent trouvé")
    
    def verifier_mots_cles_harcelement(self):
        """Vérifie la présence de mots-clés liés au harcèlement"""
        logger.info("\n🔍 Vérification des mots-clés harcèlement...")
        
        mots_cles = ["harassment", "bullying", "cyberbullying", "abuse", "threats", "harcèlement"]
        
        for mot_cle in mots_cles:
            try:
                posts = self.db_manager.search_posts(mot_cle, limit=10)
                if posts:
                    logger.info(f"  🔍 '{mot_cle}': {len(posts)} documents trouvés")
                else:
                    logger.info(f"  🔍 '{mot_cle}': 0 documents")
            except Exception as e:
                logger.warning(f"  ⚠️ Erreur recherche '{mot_cle}': {e}")
    
    def generer_rapport_etape1(self):
        """Génère un rapport complet de l'Étape 1"""
        logger.info("📋 RAPPORT DE VÉRIFICATION - ÉTAPE 1")
        logger.info("=" * 50)
        
        stats = self.db_manager.get_statistics()
        
        # Statistiques générales
        total_posts = stats.get('total_posts', 0)
        logger.info(f"📊 Total documents collectés: {total_posts}")
        
        if total_posts == 0:
            logger.error("❌ ÉCHEC: Aucune donnée collectée")
            return False
        
        # Vérifications détaillées
        success = True
        
        try:
            self.verifier_structure_donnees()
            self.verifier_plateformes()
            self.verifier_sources()
            self.verifier_donnees_recentes()
            self.verifier_mots_cles_harcelement()
        except Exception as e:
            logger.error(f"❌ Erreur lors de la vérification: {e}")
            success = False
        
        # Résumé final
        logger.info("\n" + "=" * 50)
        if success and total_posts > 0:
            logger.info("✅ ÉTAPE 1 VÉRIFIÉE AVEC SUCCÈS")
            logger.info("✅ Données collectées et stockées correctement")
            logger.info("✅ Structure des données conforme")
            logger.info("✅ Champs requis présents: titre, contenu, auteur, date, URL")
        else:
            logger.error("❌ ÉTAPE 1 INCOMPLÈTE")
        
        logger.info("=" * 50)
        return success
    
    def nettoyer(self):
        """Nettoyage"""
        self.db_manager.close_connection()

def main():
    """Point d'entrée principal"""
    verificateur = VerificateurEtape1()
    
    try:
        success = verificateur.generer_rapport_etape1()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"Erreur: {e}")
        return 1
    finally:
        verificateur.nettoyer()

if __name__ == "__main__":
    import sys
    sys.exit(main())
