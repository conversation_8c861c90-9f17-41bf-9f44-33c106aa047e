#!/usr/bin/env python3
"""
ÉTAPE 3 - Traitement NLP
Analyse de la langue et du sentiment des données prétraitées
Ajoute les champs 'langue' et 'sentiment' à chaque document
"""

import logging
from pymongo import MongoClient
from datetime import datetime
from textblob import TextBlob
from langdetect import detect
from langdetect.lang_detect_exception import LangDetectException
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AnalyseurNLP:
    def __init__(self):
        """Initialise l'analyseur NLP"""
        # Configuration MongoDB
        self.mongo_client = MongoClient("mongodb://localhost:27017/")
        self.db = self.mongo_client["harcelement"]
        self.collection = self.db["posts"]
        
        # Initialiser les analyseurs de sentiment
        self.vader_analyzer = SentimentIntensityAnalyzer()
        
        # Langues supportées
        self.langues_supportees = {
            'en': 'anglais',
            'fr': 'français',
            'es': 'espagnol',
            'de': 'allemand',
            'it': 'italien',
            'pt': 'portugais',
            'nl': 'néerlandais',
            'ru': 'russe',
            'ar': 'arabe',
            'zh': 'chinois',
            'ja': 'japonais',
            'ko': 'coréen'
        }
        
        logger.info("Analyseur NLP initialisé")
        logger.info(f"Langues supportées: {len(self.langues_supportees)}")
    
    def detecter_langue(self, texte):
        """Détecte la langue d'un texte"""
        if not texte or len(texte.strip()) < 3:
            return 'inconnu', 'inconnu'
        
        try:
            # Utiliser langdetect pour détecter la langue
            code_langue = detect(texte)
            nom_langue = self.langues_supportees.get(code_langue, code_langue)
            return code_langue, nom_langue
        except LangDetectException:
            # Si la détection échoue, essayer avec TextBlob
            try:
                blob = TextBlob(texte)
                code_langue = blob.detect_language()
                nom_langue = self.langues_supportees.get(code_langue, code_langue)
                return code_langue, nom_langue
            except:
                return 'inconnu', 'inconnu'
    
    def analyser_sentiment_vader(self, texte):
        """Analyse le sentiment avec VADER (optimisé pour les réseaux sociaux)"""
        if not texte:
            return 'neutre', 0.0, {'pos': 0.0, 'neu': 1.0, 'neg': 0.0, 'compound': 0.0}
        
        # Analyser avec VADER
        scores = self.vader_analyzer.polarity_scores(texte)
        
        # Déterminer le sentiment basé sur le score compound
        compound = scores['compound']
        if compound >= 0.05:
            sentiment = 'positif'
        elif compound <= -0.05:
            sentiment = 'négatif'
        else:
            sentiment = 'neutre'
        
        return sentiment, compound, scores
    
    def analyser_sentiment_textblob(self, texte):
        """Analyse le sentiment avec TextBlob"""
        if not texte:
            return 'neutre', 0.0, 0.0
        
        try:
            blob = TextBlob(texte)
            polarite = blob.sentiment.polarity  # -1 (négatif) à 1 (positif)
            subjectivite = blob.sentiment.subjectivity  # 0 (objectif) à 1 (subjectif)
            
            # Déterminer le sentiment
            if polarite > 0.1:
                sentiment = 'positif'
            elif polarite < -0.1:
                sentiment = 'négatif'
            else:
                sentiment = 'neutre'
            
            return sentiment, polarite, subjectivite
        except:
            return 'neutre', 0.0, 0.0
    
    def analyser_document(self, document):
        """Analyse un document complet (langue + sentiment)"""
        try:
            # Récupérer le texte à analyser (prétraité si disponible, sinon original)
            texte_analyse = document.get('texte_complet_pretraite', '')
            if not texte_analyse:
                # Fallback sur le texte original
                titre = document.get('titre', '')
                contenu = document.get('contenu', '')
                texte_analyse = f"{titre} {contenu}".strip()
            
            if not texte_analyse:
                logger.warning(f"Document {document.get('_id')} sans texte à analyser")
                return None
            
            # 1. Détection de langue
            code_langue, nom_langue = self.detecter_langue(texte_analyse)
            
            # 2. Analyse de sentiment avec VADER
            sentiment_vader, score_compound, scores_vader = self.analyser_sentiment_vader(texte_analyse)
            
            # 3. Analyse de sentiment avec TextBlob
            sentiment_textblob, polarite, subjectivite = self.analyser_sentiment_textblob(texte_analyse)
            
            # 4. Sentiment final (consensus ou VADER par défaut)
            sentiment_final = sentiment_vader
            if sentiment_vader != sentiment_textblob:
                # En cas de désaccord, privilégier VADER pour les réseaux sociaux
                logger.debug(f"Désaccord sentiment: VADER={sentiment_vader}, TextBlob={sentiment_textblob}")
            
            # Créer le document mis à jour
            document_nlp = document.copy()
            document_nlp.update({
                # Langue
                'langue_code': code_langue,
                'langue_nom': nom_langue,
                
                # Sentiment final
                'sentiment': sentiment_final,
                'sentiment_score': score_compound,
                
                # Détails VADER
                'sentiment_vader': {
                    'sentiment': sentiment_vader,
                    'compound': score_compound,
                    'positif': scores_vader['pos'],
                    'neutre': scores_vader['neu'],
                    'negatif': scores_vader['neg']
                },
                
                # Détails TextBlob
                'sentiment_textblob': {
                    'sentiment': sentiment_textblob,
                    'polarite': polarite,
                    'subjectivite': subjectivite
                },
                
                # Métadonnées
                'date_analyse_nlp': datetime.now(),
                'nlp_effectue': True
            })
            
            return document_nlp
            
        except Exception as e:
            logger.error(f"Erreur analyse NLP document {document.get('_id', 'unknown')}: {e}")
            return None
    
    def analyser_tous_documents(self):
        """Analyse tous les documents de la collection"""
        logger.info("=" * 60)
        logger.info("DÉBUT ANALYSE NLP - ÉTAPE 3")
        logger.info("=" * 60)
        
        # Compter les documents à traiter
        total_documents = self.collection.count_documents({})
        documents_non_analyses = self.collection.count_documents({"nlp_effectue": {"$ne": True}})
        
        logger.info(f"Total documents en base: {total_documents}")
        logger.info(f"Documents à analyser: {documents_non_analyses}")
        
        if documents_non_analyses == 0:
            logger.info("Tous les documents sont déjà analysés")
            return 0
        
        documents_analyses = 0
        documents_erreur = 0
        
        # Statistiques par langue et sentiment
        stats_langues = {}
        stats_sentiments = {'positif': 0, 'neutre': 0, 'négatif': 0}
        
        # Traiter les documents non analysés
        cursor = self.collection.find({"nlp_effectue": {"$ne": True}})
        
        for document in cursor:
            document_nlp = self.analyser_document(document)
            
            if document_nlp:
                # Mettre à jour le document dans la base
                self.collection.replace_one(
                    {"_id": document["_id"]}, 
                    document_nlp
                )
                documents_analyses += 1
                
                # Mettre à jour les statistiques
                langue = document_nlp.get('langue_nom', 'inconnu')
                sentiment = document_nlp.get('sentiment', 'neutre')
                
                stats_langues[langue] = stats_langues.get(langue, 0) + 1
                if sentiment in stats_sentiments:
                    stats_sentiments[sentiment] += 1
                
                if documents_analyses % 10 == 0:
                    logger.info(f"Analysés: {documents_analyses}/{documents_non_analyses}")
            else:
                documents_erreur += 1
        
        # Résumé
        logger.info("=" * 60)
        logger.info("RÉSUMÉ ANALYSE NLP")
        logger.info("=" * 60)
        logger.info(f"Documents analysés avec succès: {documents_analyses}")
        logger.info(f"Documents en erreur: {documents_erreur}")
        logger.info(f"Total documents analysés en base: {self.collection.count_documents({'nlp_effectue': True})}")
        
        # Statistiques langues
        logger.info(f"\nRépartition par langue:")
        for langue, count in sorted(stats_langues.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  {langue}: {count} documents")
        
        # Statistiques sentiments
        logger.info(f"\nRépartition par sentiment:")
        for sentiment, count in stats_sentiments.items():
            pourcentage = (count / documents_analyses * 100) if documents_analyses > 0 else 0
            logger.info(f"  {sentiment}: {count} documents ({pourcentage:.1f}%)")
        
        return documents_analyses
    
    def afficher_exemples_nlp(self, limite=3):
        """Affiche des exemples d'analyse NLP"""
        logger.info("\n" + "🔍 EXEMPLES D'ANALYSE NLP")
        logger.info("-" * 50)
        
        # Récupérer quelques documents analysés
        documents = list(self.collection.find({"nlp_effectue": True}).limit(limite))
        
        for i, doc in enumerate(documents, 1):
            logger.info(f"\nExemple {i}:")
            logger.info(f"Titre: {doc.get('titre', '')[:60]}...")
            logger.info(f"Langue: {doc.get('langue_nom', 'N/A')} ({doc.get('langue_code', 'N/A')})")
            logger.info(f"Sentiment: {doc.get('sentiment', 'N/A')} (score: {doc.get('sentiment_score', 0):.3f})")
            
            # Détails sentiment
            vader = doc.get('sentiment_vader', {})
            textblob = doc.get('sentiment_textblob', {})
            logger.info(f"VADER: {vader.get('sentiment', 'N/A')} (compound: {vader.get('compound', 0):.3f})")
            logger.info(f"TextBlob: {textblob.get('sentiment', 'N/A')} (polarité: {textblob.get('polarite', 0):.3f})")
    
    def obtenir_statistiques_nlp(self):
        """Obtient les statistiques complètes de l'analyse NLP"""
        total = self.collection.count_documents({})
        analyses = self.collection.count_documents({"nlp_effectue": True})
        
        logger.info(f"\n📊 STATISTIQUES NLP")
        logger.info(f"Total documents: {total}")
        logger.info(f"Documents analysés: {analyses}")
        logger.info(f"Pourcentage analysé: {(analyses/total*100):.1f}%" if total > 0 else "0%")
        
        # Statistiques détaillées par langue
        pipeline_langue = [
            {"$match": {"nlp_effectue": True}},
            {"$group": {"_id": "$langue_nom", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        logger.info(f"\nLangues détectées:")
        for result in self.collection.aggregate(pipeline_langue):
            logger.info(f"  {result['_id']}: {result['count']} documents")
        
        # Statistiques détaillées par sentiment
        pipeline_sentiment = [
            {"$match": {"nlp_effectue": True}},
            {"$group": {"_id": "$sentiment", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        logger.info(f"\nSentiments détectés:")
        for result in self.collection.aggregate(pipeline_sentiment):
            pourcentage = (result['count'] / analyses * 100) if analyses > 0 else 0
            logger.info(f"  {result['_id']}: {result['count']} documents ({pourcentage:.1f}%)")
    
    def fermer_connexions(self):
        """Ferme les connexions"""
        self.mongo_client.close()
        logger.info("Connexions fermées")

def main():
    """Fonction principale"""
    analyseur = None
    
    try:
        # Initialiser l'analyseur
        analyseur = AnalyseurNLP()
        
        # Lancer l'analyse NLP
        documents_analyses = analyseur.analyser_tous_documents()
        
        # Afficher des exemples
        analyseur.afficher_exemples_nlp()
        
        # Afficher les statistiques
        analyseur.obtenir_statistiques_nlp()
        
        if documents_analyses > 0:
            logger.info("\n✅ ÉTAPE 3 TERMINÉE AVEC SUCCÈS !")
            logger.info("✅ Détection de langue effectuée")
            logger.info("✅ Analyse de sentiment effectuée")
            logger.info("✅ Champs 'langue' et 'sentiment' ajoutés")
        else:
            logger.info("✅ ÉTAPE 3 DÉJÀ COMPLÈTE - Tous les documents sont analysés")
        
    except KeyboardInterrupt:
        logger.info("Analyse NLP interrompue par l'utilisateur")
    except Exception as e:
        logger.error(f"Erreur: {e}")
    finally:
        if analyseur:
            analyseur.fermer_connexions()

if __name__ == "__main__":
    main()
